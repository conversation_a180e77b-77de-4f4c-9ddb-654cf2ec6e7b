'use client';

import { AnimatedText } from '@/components/ui/animated-text';
import Image from 'next/image';
import { Button } from '../../components/ui/button';
import { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { X } from 'lucide-react';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { FadeInWhenVisible } from '@/components/ui/fade-in-when-visible';

export default function GastronomyPage() {
  const [isObanModalOpen, setIsObanModalOpen] = useState(false);
  const [isCellarModalOpen, setIsCellarModalOpen] = useState(false);
  return (
    <div className="min-h-screen bg-secondary dark:bg-[#0e0e0e] text-[#1a1a1a] dark:text-[#FFFFE3]">
      {/* Hero Section */}
      <section
        className="relative h-[65vh] bg-fixed bg-center bg-cover flex items-center justify-center"
        style={{
          backgroundImage: "url('/images/sliders/5.webp')", // replace with your luxury gastronomy hero image
        }}
      >
        <FadeInWhenVisible delay={0}>
          <div className="bg-black/60 p-6 rounded-xl text-center max-w-3xl mx-auto">
            <div className='w-fit mx-auto'>
              <h1 className="text-4xl md:text-5xl font-serif uppercase text-[#FFFFE3] tracking-wider mb-4">Gastronomy</h1>
              <svg id="a" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 208.33 7.34" className="w-80 text-[#FFFFE3] mx-auto my-3 block align-middle leading-none" fill="currentColor"><path d="M104.43,2.94c-1.86-1.1-3.91-1.79-6.01-2.13-2.11-.33-4.26-.25-6.38-.05-4.25.47-8.39,1.61-12.47,2.92l-6.13,2c-2.04.63-4.13,1.34-6.35,1.57-4.38.48-9.05-.73-12.37-3.92l.41.18-13.65-.04c-4.55-.03-9.1-.01-13.65-.07l-13.65-.16c-4.55-.1-9.63-.15-14.18-.28,4.55-.13,9.63-.19,14.18-.28l13.65-.16c4.55-.06,9.1-.05,13.65-.07l13.65-.04c.15,0,.29.06.39.16l.03.03c1.4,1.39,3.3,2.48,5.27,3.07,1.98.62,4.09.81,6.17.6,2.08-.2,4.1-.86,6.16-1.48,2.05-.64,4.11-1.28,6.17-1.88,4.13-1.21,8.35-2.29,12.67-2.64,2.16-.12,4.34-.11,6.48.3,2.12.42,4.17,1.22,5.96,2.42Z" /><path d="M117.55,2.84c1.79-1.18,3.85-1.96,5.97-2.33,2.12-.39,4.3-.43,6.45-.29,4.31.31,8.52,1.38,12.65,2.53l6.17,1.81c2.06.61,4.09,1.23,6.16,1.38,4.13.44,8.41-.54,11.88-3.09-1.39,1.68-3.36,2.81-5.44,3.53-2.1.66-4.36.89-6.55.65-2.2-.24-4.29-.95-6.33-1.55l-6.13-1.93c-4.08-1.25-8.22-2.37-12.46-2.8-4.2-.45-8.66-.11-12.38,2.1Z" /><path d="M104.43,2.94c1.79-1.2,3.84-2,5.96-2.42,2.13-.42,4.32-.42,6.48-.3,4.33.35,8.54,1.43,12.67,2.64l6.17,1.88c2.06.62,4.08,1.28,6.16,1.48,2.08.21,4.19.02,6.17-.59,1.97-.59,3.87-1.68,5.27-3.07l.02-.02c.11-.11.25-.16.39-.16l13.97.04,13.97.07,12.68.16c4.66.09,9.31.15,13.97.28-4.66.13-9.32.19-13.97.28l-12.68.16-13.97.07-13.97.04.41-.18c-3.31,3.19-7.98,4.4-12.37,3.92-2.21-.23-4.31-.94-6.35-1.57l-6.13-2c-4.08-1.31-8.22-2.45-12.47-2.92-2.12-.2-4.27-.27-6.38.06-2.1.35-4.15,1.03-6.01,2.14Z" /><path d="M43.87,2.84c1.64,1.38,3.6,2.33,5.64,2.82,2.04.54,4.17.65,6.24.39,2.08-.26,4.09-.9,6.14-1.53l6.14-1.91c4.13-1.17,8.34-2.25,12.68-2.56,2.16-.1,4.35-.09,6.49.33,2.12.46,4.18,1.24,5.95,2.46-1.9-1-3.95-1.64-6.04-1.91-2.1-.3-4.23-.21-6.33-.01-2.1.26-4.19.61-6.27,1.09-2.07.5-4.12,1.05-6.17,1.68l-6.16,1.81c-2.05.58-4.15,1.27-6.35,1.41-2.18.17-4.4-.05-6.48-.72-1.04-.33-2.03-.79-2.96-1.35-.93-.55-1.81-1.2-2.53-2Z" /><path d="M7,1.78c-1.43,1.05-4.47,1.16-4.47,1.16,0,0,3.05.11,4.47,1.16,1.43,1.05,2.32-.24,1.67-1.16.65-.92-.24-2.22-1.67-1.16Z" /><path d="M2.94,2.94c0,.39-.32.71-.71.71s-.71-.32-.71-.71.32-.71.71-.71.71.32.71.71Z" /><path d="M201.32,4.11c1.43-1.05,4.48-1.16,4.48-1.16,0,0-3.05-.11-4.48-1.16-1.43-1.05-2.32.24-1.66,1.16-.66.92.23,2.22,1.66,1.16Z" /><path d="M205.39,2.94c0-.39.32-.71.71-.71s.71.32.71.71-.32.71-.71.71-.71-.32-.71-.71Z" /></svg>
            </div>
            <AnimatedText text='An intimate culinary journey within the timeless charm of Riad Noir d’Ivoire. Savor the Maison’s signature experiences — where Moroccan heritage meets global refinement.' className="text-[#FFFFE3] text-lg leading-relaxed font-light tracking-wide mt-2 leading-snug" />
          </div>
        </FadeInWhenVisible>
      </section>

      {/* Cards Section */}
      <FadeInWhenVisible delay={0}>

        <section className="container mx-auto px-4 lg:px-30 xl:px-50 py-20">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Experiences</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-12 mt-10">
            {/* Ôban */}
            <div className="bg-secondary/90 dark:bg-[#1a1a1a]/90 border border-black/20 dark:border-[#FFFFE3]/20 rounded-xl shadow-xl overflow-hidden flex flex-col">
              <div className="relative w-full h-80">
                <Image
                  src="/images/gastronomy/oban.jpg"
                  alt="Ôban Restaurant"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  className="object-cover h-80"
                />
              </div>
              <div className="p-6 flex flex-col justify-between min-h-80">
                <div className='flex flex-col gap-4'>
                  <h2 className="text-2xl font-serif text-black dark:text-primary uppercase tracking-widest">Ôban</h2>
                  <AnimatedText text="We currently offer an all-day and room service menu plus in the evening an a la carte dinner menu. In February 2019,
               we’ll be launching our new restaurant Ōban where fine seasonal food created using a combination of international recipes and techniques with local products and flavours will be served. 
               "className="text-black dark:text-primary font-light leading-relaxed text-[15px]" />
                </div>
                <div className="w-full flex justify-center">
                  <Button
                    onClick={() => setIsObanModalOpen(true)}
                    className="text-sm font-semibold py-2 px-5 rounded-full border border-white hover:bg-[#FFFFE3] hover:text-black transition"
                  >
                    CHECK MENU
                  </Button>
                </div>

              </div>
            </div>

            {/* The Cellar */}
            <div className="bg-secondary/90 dark:bg-[#1a1a1a]/90 border border-black/20 dark:border-[#FFFFE3]/20 rounded-xl shadow-xl overflow-hidden flex flex-col">
              <div className="relative w-full h-80">
                <Image
                  src="/images/gastronomy/cellar.jpg"
                  alt="The Cellar"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  className="object-cover h-80"
                />
              </div>
              <div className="p-6 flex flex-col justify-between min-h-80">
                <div className='flex flex-col gap-4'>
                  <h2 className="text-2xl font-serif uppercase tracking-widest text-black dark:text-primary">The Cellar</h2>
                  <AnimatedText text="One of a kind in Morocco and designed especially for us, our unique, open design wine cellar holds 3000 bottles —including, 
              for example, a ’62 Cheval Blanc or ’99 Petrus— all which can be enjoyed by the glass with the all new Coravin and iFAVINE systems. 
              We are a member of the Le Cercle SGC and are currently working alongside our Sommelier to fill our wine cellar with varietals from all over the World."
                    className="text-black dark:text-primary font-light leading-relaxed text-[15px]">

                  </AnimatedText>
                </div>

                <div className="w-full flex justify-center">
                  <Button
                    onClick={() => setIsCellarModalOpen(true)}
                    className="text-sm font-semibold py-2 px-5 rounded-full border border-white hover:bg-[#FFFFE3] hover:text-black transition"
                  >
                    Wine Cellar
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Optional Quote */}
          <div className="text-black dark:text-primary text-center mt-16 text-lg font-light italic opacity-80">
            “We work with passion to bring the finest from the world to your glass.”<br />
            <span className="text-sm mt-2 block font-serif">— Noir D Ivoire</span>
          </div>
        </section>
      </FadeInWhenVisible>
      <Dialog open={isObanModalOpen} onClose={() => setIsObanModalOpen(false)} className="relative z-50 h-[300px]">
        <div className="fixed inset-0 bg-black/60" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4 overflow-y-auto">
          <Dialog.Panel className="max-w-5xl w-full bg-secondary dark:bg-[#1a1a1a] p-10 rounded-lg shadow-xl relative font-serif text-[15px] text-[#1a1a1a] dark:text-[#FFFFE3]">
            <button
              onClick={() => setIsObanModalOpen(false)}
              className="absolute top-11 right-4 text-gray-600 hover:text-black dark:text-gray-300 dark:hover:text-white"
            >
              <X size={24} />
            </button>

            <Dialog.Title className="tracking-widest mb-10">
              <div className="flex justify-center">
                <svg
                  id="a"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 218.29 111.29"
                  className="w-[600px] h-[100px] text-[#FFFFE3]"
                >
                  <defs>
                    <clipPath id="b"><rect x=".29" y=".29" width="218" height="110.74" fill="none" stroke-width="0" />
                    </clipPath>
                    <clipPath id="c"><rect x=".29" y=".29" width="218" height="111" fill="none" stroke-width="0" />
                    </clipPath></defs><g clip-path="url(#b)"><g clip-path="url(#c)">
                      <path d="M68.05,77.66q-1.26-.36-1.24,1.48c1.15.5,2.32,1,3.34,1.44.67,3.28.32,4.36-1.47,5.17-2.58,1.16-4.38.33-7.24-3.47-.59.09-1.31.16-2.01.3-11.55,2.32-23.38-3.32-27.97-14.08-3.51-8.22-2.77-16.1,2.25-23.58,2.93-4.36,6.79-7.4,11.7-8.96,2.44-3.23,2.54-3.2,6.4-1.98,1.14.36,2.45.3,3.67.3,10.95.02,20.75,8.05,22.08,18.85.41,3.35.42,6.74.65,***********,1.36,1.97,2.26,3.26-.51,1.02-.95,1.89-1.49,2.97-1.01-.3-2-.6-3-.89,0-.34,0-.68,0-**********,1.67.66,2.5.99l.06.08s-.08-.06-.08-.06c.05-.72.1-1.43.15-2.16h-2.28c-.11.38-.22.77-.34,1.16-.16.33-.33.67-.49,1,0,0-.08.06-.08.06l.06-.08c.17,0,.34,0,.52,0-.74,1.45-1.49,2.9-2.27,**********.***********.76,1.45.52,2.84-.69,3.78-1.2.94-2.68,1.26-4.12.23-.4-.29-.81-.56-1.22-.84l.02.02ZM49.67,53.74l.06.08-.09-.06c-.17-.17-.33-.33-.5-.5-.12-2.62-.09-5.2,1.49-7.48l.08-.05-.06.08c-3.69,1.42-4.29,4.42-1.49,**********.33.33.5.5ZM72.01,72.18l-.49.49h-.01c.16-.18.33-.34.49-.51.17-.17.34-.34.5-.5.33-.16.66-.33.99-.49h.01c-.33.18-.66.34-.99.51-.17.17-.33.34-.5.5ZM59.59,56.24h-.01c-.17-.15-.33-.32-.5-.48-.17-.17-.33-.33-.5-.5-.17-.7-.33-1.4-.51-2.17h-2.3c-2.28,2.71-4.21,3.43-6.3,2.13-.86-.53-1.66-1.39-2.12-2.3-1.07-2.09-.86-4.27.4-6.48-2.13-.17-3.68-.86-3.97-2.96-.27-2,1.16-2.69,2.67-3.36.32-.14.4-.82.8-1.7-1.17.35-1.84.43-2.37.74-5.12,2.96-9.28,6.83-11.13,12.65-.75,2.34-1.95,4.85-1.7,7.15,1.16,10.83,7.18,17.4,17.66,20.06,3.75.95,7.76.84,11.89,1.23v-4.48c.94-.56,2.02-1.21,3.14-1.88-.72-1.37-1.37-2.1-2.65-1.2l-.02-.02.22,1.14c-.21.05-.42.1-.63.15-.18-.66-.36-1.33-.6-2.19,2.93-.34,1.33-3.26,2.94-4.77.28.84.43,1.27.57,1.71l-.08.07.06-.09c.62-.05,1.25-.09,1.64-.12v-3.29h7.97c-2.17-.93-4.62-.97-5.64-3.55.07-.11.14-.21.12-.18.01-.03-.04.09-.1.21-1.05-2.48,1.4-3.53,2.22-5.18-2.41-3.14-2.35-3.98.27-4.8,0,.17,0,.34,0,.52-1.15.41-1.12,1.12-.5,1.99,1.44,1.55,2.69,1.85,3.74.87,1.03-.96.8-2.21-.71-4.19-.87.29-1.71.57-2.55.85-.11-.54-.22-1.07-.34-1.62.79-.52,1.46-.95,2.15-1.41-.19-.51-.31-.9-.47-1.27-.1-.21-.28-.39-.41-.57-2.36,1.86-2.96,1.79-4.26-.39.39-1.05.77-2.1,1.14-3.09-1.1-.81-1.99-1.47-3.08-2.27-.07.71-.12,1.19-.17,1.66-.33.33-.66.66-1,.99-2.24.17-4.47.33-6.55.49,2.56,2.42,4.17,2.57,6.55.49.27.02.76.08.76.06.1-.68.15-1.37.21-2.06,1.8,2.72-1.55,4.25-1.36,6.6-.29.24-.58.48-.87.72,1.02.61,2.03,1.23,3.18,1.92-.89.74-1.62,1.36-2.35,1.97,2.75,3.03,2.51,4.46-1.13,5.61-1.18-2.37-4.94-1.96-5.79-5.31.6.4,1,.67,1.4.93l.5.5.49.5ZM70.03,70.2c-.33.16-.66.33-1,.49l-1.74.53c.08.17.17.34.25.51.49-.35.98-.7,1.47-1.06l1-.49.02.02ZM49.15,44.29c.38-.05.77-.1,1.15-.15l-.1-.34c-.34.17-.69.34-1.03.51l-.08.05s.06-.08.06-.08ZM56.6,39.81c.58-.54,1.16-1.09,1.74-1.63-.18-.15-.37-.29-.55-.44-.39.7-.78,1.4-1.17,2.1-.18,0-.35,0-.53,0,.05-.73.11-1.46.16-2.19-.25-.03-.49-.05-.74-.08-.12.84-.25,1.68-.42,2.84.61-.36.82-.48,1.02-.6.05.12.1.25.14.37.11-.12.22-.25.33-.37ZM55.11,49.79c-.05-.13-.11-.25-.09-.22-.02-.04.05.08.12.2.36,1.41,1.17,2.23,2.74,2.06-.92-.68-1.85-1.36-2.77-2.04ZM57.6,41.3c.96-.66,1.91-1.32,2.87-1.98-.16-.2-.33-.39-.49-.59-.78.86-1.57,1.73-2.35,2.59l-.09.06s.07-.08.07-.08ZM69.03,45.78c-.2.22-.41.44-.66.72q1.93.62,1.84-1.63c-.46.35-.83.64-1.21.94l.02-.02ZM54.15,41.3c.14.54.17,1.15.47,1.58.1.15.84-.13,1.29-.21.02-.23.05-.47.07-.7-.62-.21-1.24-.43-1.86-.64-.09-1.1-.19-2.21-.28-3.31-.21.03-.42.07-.62.1-.53,1.28-1.15,2.58.93,3.18ZM56.6,48.3l-.06-.08.08.06c.37,1.88,1.97,2.24,3.45,2.88.67-.3,1.34-.6,2.01-.89-.33,1.41.37,2.06,2.21,2.52-.98-1.09-1.61-1.79-2.23-2.49-1.82-.66-3.64-1.32-5.46-1.99ZM52.63,54.25c.53-.57,1.07-1.14,1.59-1.7-2.04-2.09-2.41-2.1-3.79-.2.51-.04.86-.07,1.21-.09.17,0,.33,0,.5,0,1.28.39.81,1.22.51,2.02-.38.05-.76.1-1.14.15l.1.33c.34-.17.68-.33,1.03-.5ZM69.51,76.67c-.04-.12-.08-.24-.03-.08-.12-.13-.03-.04.05.06.77,1.37,1.9,1.31,2.84.4.79-.77.5-1.7-.56-2.17-.23-.1-.6.13-1.03.24.19.56.34,1,.59,1.78-.83-.1-1.35-.16-1.87-.22ZM61.07,41.33l.02-.02c-.32.39-.63.78-1.04,1.28,2.62.23,4.05-.21,4.3-1.32.28-1.22-.46-1.59-1.88-1.63-.37.45-.89,1.07-1.4,1.69ZM65.56,75.67c-2.48,1.44-2.78,3.75-2.37,6.25.38,2.34,2.76,3.71,4.8,2.92,1.42-.55,2.22-1.92,1.72-3.1-.52-1.23-1.51-1.83-2.78-1.36-1.61.59-1.37,1.83-.64,3.15-2.15-1.23-.77-2.7-.76-4.07v-3.8l.02.02ZM71.47,61.11c.1-.19.21-.38.31-.58.71.19,1.41.38,2.12.57.16-.17.32-.34.48-.51-.28-.64-.4-1.58-.89-1.84-.65-.35-1.58-.18-2.32-.23-.29,1.27-.49,2.13-.68,2.95,2,2.38,3.19,2.61,4.4,1.02-1.09-.44-2.25-.91-3.42-1.38ZM61.58,55.03c.57-2.55.13-3.08-2.28-2.82-.31,1.85,1.1,2.16,2.28,2.82ZM46.95,41.49c-1.68.3-1.87,1.22-1.58,2.4.03.13.2.23.36.41.13-.55.24-1.03.35-1.51.68.21,1.2.37,1.72.54.1-.14.2-.29.3-.43-.41-.5-.81-1-1.15-1.41ZM51.01,40.22c.31-.21.62-.42.93-.63-.49-.71-.97-1.42-1.46-2.14-.3.2-.59.4-.89.59.47.72.94,1.45,1.41,2.17ZM61.13,47.54c.01.19.02.39.03.58.55-.05,1.1-.08,1.64-.16.04,0,.04-.27.08-.61-.61.06-1.19.12-1.76.18ZM63.43,57.7c.21.05.43.1.64.15.02-.36.04-.73.06-1.09-.15-.02-.31-.03-.46-.05-.08.33-.17.66-.25.99ZM65.61,70.91v-.49c-.24.07-.46.14-.68.21.02.09.05.19.07.28h.61ZM52.01,42.61c.06-.06.12-.13.17-.19-.14-.13-.28-.26-.43-.39-.06.06-.12.13-.18.19.14.13.28.26.43.39ZM50.97,48.33c-.06.03-.12.06-.19.08.07.18.14.35.21.53.08-.04.16-.07.24-.11-.09-.17-.17-.34-.26-.51ZM75.33,57.08c-.06-.12-.13-.23-.19-.35-.09.1-.19.2-.26.31-.02.03.08.18.11.18.12-.03.23-.09.35-.14ZM67.61,67.43c.12.01.24.02.35.03-.12-.01-.24-.02-.35-.03Z" /><path d="M30.22,28.33h7.07c0-.2,0-.4,0-.6h-11.18c-.5-1.28-.95-2.45-1.63-4.21,3.18,0,5.76-.04,8.34.02,1.23.02,2.46.31,3.69.32,8.77.04,17.54.17,26.3-.02,4.35-.1,8.7-.73,13.03-1.29,3.46-.45,5.93.71,6.97,3.64-.57,2.23-2.53,2.44-4.28,2.67-3.67.47-7.48.29-11.03,1.2-4.57,1.18-9.03.16-13.54.3-4.94.15-9.9.15-14.84-.04-3-.11-5.98-.7-8.98-1.08.03-.3.05-.6.08-.89ZM44.69,28.04c0,.06,0,.12,0,.18h2.51c0-.06,0-.12,0-.18h-2.51Z" /><path d="M177.28,68.51h-2.34v-21.37c.48-.16.78-.34,1.09-.35.49-.03.98.05,1.85.12,3.44,5.52,6.96,11.18,10.5,16.84v-16.73c.82-.06,1.63-.12,2.68-.19v21.51c-1.92.84-3.12.26-4.07-1.34-2.98-5.05-6-10.07-9.01-15.1-.07-.12-.25-.16-.7-.43v17.05Z" /><path d="M106.56,56.99c3.55,1.85,4.22,3.41,3.59,7.25-.4,2.43-3.37,4.39-6.46,4.51-2.13.08-4.26.02-6.66.02v-21.04c2.58-1.84,9.51-1.21,11.49,1.16,2.07,2.47,1.65,6.07-1.96,8.12ZM100.04,66.32c2.82.66,5.35.42,6.69-1.82.65-1.09.13-3.22-.47-4.61-.62-1.42-4.04-1.79-6.21-1.02v7.45ZM100.11,55.8c2,.72,3.63.32,5.14-.67,1.85-1.21,2.02-4.16.17-5.22-1.52-.88-3.27-1.54-5.31-.51v6.4Z" /><path d="M140.22,46.86h3.43c2.44,7.16,4.86,14.25,7.39,21.68-1.13.07-1.9.12-2.85.17-.91-2.32-1.77-4.52-2.66-6.77h-7.17c-.83,2.13-1.69,4.33-2.57,6.59h-2.9c2.5-7.39,4.89-14.46,7.34-21.68ZM138.88,59.48h5.96c-1-3.15-1.92-6.06-3-9.47-1.08,3.45-1.98,6.33-2.96,9.47Z" /><polygon points="68.02 77.64 67.96 77.72 68.05 77.66 68.02 77.64" /><path d="M67.78,82.55c0-.12,0-.25,0-.37.04.06.12.12.11.19,0,.06-.08.12-.12.18Z" /><path d="M71,53.78c0-.51,0-1.03,0-1.54,0,.51,0,1.03,0,1.54Z" /><path d="M70.5,54.27c.18-.18.36-.36.53-.54-.18.18-.36.36-.53.54Z" /><path d="M64.54,43.78c0,.35,0,.69,0,1.04,0-.35,0-.69,0-1.04Z" /><path d="M52.16,52.28c-.18,0-.36,0-.54,0,.18,0,.36,0,.54,0Z" /><polygon points="69.01 45.8 69.11 45.87 69.03 45.78 69.01 45.8" /></g></g></svg>
              </div>

              <h1 className='text-3xl font-serif uppercase  text-center text-[#1a1a1a] dark:text-[#FFFFE3] '>
                Ôban – Menu
              </h1>
              <div className="text-xs text-center italic opacity-70 pt-4">
                Prices are in Moroccan dirhams
              </div>
            </Dialog.Title>

            <div className="max-h-[75vh] overflow-y-auto space-y-16 px-4 md:px-16">
              {/* Moroccan Specialties */}
              <div>
                <h2 className="text-center text-xl font-semibold uppercase mb-8 tracking-wider">Moroccan Specialties</h2>

                <div className="mb-6">
                  <h3 className="text-center italic mb-4">Starters</h3>
                  <ul className="space-y-3">
                    <li className="flex justify-between"><span>Seafood pastilla with mushroom and vermicelli</span><span>180</span></li>
                    <li className="flex justify-between"><span>Selection of Moroccan salads</span><span>120</span></li>
                    <li className="flex justify-between"><span>“Harira Marrakchia” with stuffed dates and chebakia</span><span>110</span></li>
                    <li className="flex justify-between"><span>Briouattes (savory filo pastry parcels)</span><span>120</span></li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-center italic mb-4">Main Dishes</h3>
                  <ul className="space-y-3">
                    <li className="flex justify-between"><span>Baby chicken with lemon confit and green olives</span><span>160</span></li>
                    <li className="flex justify-between"><span>Monkfish tagine with dates and onions</span><span>220</span></li>
                    <li className="flex justify-between"><span>Lamb tagine with apricot and walnuts</span><span>180</span></li>
                    <li className="flex justify-between"><span>Chicken couscous with raisins and chickpeas</span><span>170</span></li>
                    <li className="flex justify-between"><span>Lamb couscous with seven vegetables</span><span>180</span></li>
                  </ul>
                </div>
              </div>

              {/* International Cuisine */}
              <div>
                <h2 className="text-center text-xl font-semibold uppercase mb-8 tracking-wider">International Cuisine</h2>

                <div className="mb-6">
                  <h3 className="text-center italic mb-4">Starters</h3>
                  <ul className="space-y-3">
                    <li className="flex justify-between"><span>Caesar salad with teriyaki chicken</span><span>80</span></li>
                    <li className="flex justify-between"><span>Quinoa salad with orange and feta</span><span>90</span></li>
                    <li className="flex justify-between"><span>Beef carpaccio with artichoke and baby rucola</span><span>120</span></li>
                    <li className="flex justify-between"><span>Soup of the day</span><span>85</span></li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-center italic mb-4">Main Dishes</h3>
                  <ul className="space-y-3">
                    <li className="flex justify-between"><span>Grilled sea bass with vegetables</span><span>260</span></li>
                    <li className="flex justify-between"><span>Piccata breaded chicken</span><span>170</span></li>
                    <li className="flex justify-between"><span>Beef tenderloin, potato, mushrooms, red wine</span><span>220</span></li>
                  </ul>
                </div>
              </div>

              {/* Side Dishes */}
              <div>
                <h3 className="text-center italic mb-4">Side Dishes</h3>
                <ul className="space-y-3">
                  <li className="flex justify-between"><span>Steamed vegetables</span><span>45</span></li>
                  <li className="flex justify-between"><span>Grilled or steamed asparagus</span><span>55</span></li>
                  <li className="flex justify-between"><span>Steamed rice</span><span>40</span></li>
                  <li className="flex justify-between"><span>Mashed, fries, or roast potatoes</span><span>40</span></li>
                </ul>
              </div>

              {/* Pasta & Risotto */}
              <div>
                <h3 className="text-center italic mb-4">Pasta & Risotto</h3>
                <ul className="space-y-3">
                  <li className="flex justify-between"><span>Risotto with spring vegetables</span><span>140</span></li>
                  <li className="flex justify-between"><span>Risotto with mascarpone & beetroot</span><span>150</span></li>
                  <li className="flex justify-between"><span>Spaghetti Bolognese</span><span>175</span></li>
                  <li className="flex justify-between"><span>Penne Arrabbiata</span><span>155</span></li>
                </ul>
              </div>

              {/* Sandwiches & Snacks */}
              <div>
                <h3 className="text-center italic mb-4">Sandwiches & Snacks</h3>
                <ul className="space-y-3">
                  <li className="flex justify-between"><span>The Authentic Cheeseburger</span><span>150</span></li>
                  <li className="flex justify-between"><span>Salmon & avocado club sandwich</span><span>160</span></li>
                  <li className="flex justify-between"><span>Classic chicken club sandwich</span><span>130</span></li>
                  <li className="flex justify-between"><span>Rib eye sandwich, mustard seed & roasted onions</span><span>140</span></li>
                </ul>
              </div>

              {/* Desserts */}
              <div>
                <h3 className="text-center italic mb-4">Desserts</h3>
                <ul className="space-y-3">
                  <li className="flex justify-between"><span>Chocolate & pistachio ice cream</span><span>85</span></li>
                  <li className="flex justify-between"><span>Pineapple cheesecake with sorbet</span><span>80</span></li>
                  <li className="flex justify-between"><span>Tart Tatin Michalak with vanilla ice cream</span><span>75</span></li>
                  <li className="flex justify-between"><span>Palais chocolat with ice cream</span><span>70</span></li>
                  <li className="flex justify-between"><span>Saffron crème brûlée</span><span>80</span></li>
                  <li className="flex justify-between"><span>Seasonal fruit platter</span><span>70</span></li>
                  <li className="flex justify-between"><span>Homemade ice cream with hot sauce</span><span>65</span></li>
                </ul>
              </div>

              <div className="text-sm text-center italic opacity-70 pt-4">
                Prices are in Moroccan dirhams
              </div>
            </div>
          </Dialog.Panel>

        </div>
      </Dialog>

      <Dialog open={isCellarModalOpen} onClose={() => setIsCellarModalOpen(false)} className="relative z-50">
        <div className="fixed inset-0 bg-black/60" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4 overflow-y-auto">
          <Dialog.Panel className="max-w-5xl w-full bg-secondary dark:bg-[#1a1a1a] p-10 rounded-lg shadow-xl relative font-serif text-[15px] text-[#1a1a1a] dark:text-[#FFFFE3]">
            <button
              onClick={() => setIsCellarModalOpen(false)}
              className="absolute top-17 right-4 text-gray-600 hover:text-black dark:text-gray-300 dark:hover:text-white"
            >
              <X size={24} />
            </button>

            <Dialog.Title className="tracking-widest mb-10">
              <div className="flex justify-center">
                <svg
                  id="a"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 218.29 111.29"
                  className="w-[600px] h-[100px] text-[#FFFFE3]"
                >
                  <defs>
                    <clipPath id="b"><rect x=".29" y=".29" width="218" height="110.74" fill="none" stroke-width="0" />
                    </clipPath>
                    <clipPath id="c"><rect x=".29" y=".29" width="218" height="111" fill="none" stroke-width="0" />
                    </clipPath></defs><g clip-path="url(#b)"><g clip-path="url(#c)">
                      <path d="M68.05,77.66q-1.26-.36-1.24,1.48c1.15.5,2.32,1,3.34,1.44.67,3.28.32,4.36-1.47,5.17-2.58,1.16-4.38.33-7.24-3.47-.59.09-1.31.16-2.01.3-11.55,2.32-23.38-3.32-27.97-14.08-3.51-8.22-2.77-16.1,2.25-23.58,2.93-4.36,6.79-7.4,11.7-8.96,2.44-3.23,2.54-3.2,6.4-1.98,1.14.36,2.45.3,3.67.3,10.95.02,20.75,8.05,22.08,18.85.41,3.35.42,6.74.65,***********,1.36,1.97,2.26,3.26-.51,1.02-.95,1.89-1.49,2.97-1.01-.3-2-.6-3-.89,0-.34,0-.68,0-**********,1.67.66,2.5.99l.06.08s-.08-.06-.08-.06c.05-.72.1-1.43.15-2.16h-2.28c-.11.38-.22.77-.34,1.16-.16.33-.33.67-.49,1,0,0-.08.06-.08.06l.06-.08c.17,0,.34,0,.52,0-.74,1.45-1.49,2.9-2.27,**********.***********.76,1.45.52,2.84-.69,3.78-1.2.94-2.68,1.26-4.12.23-.4-.29-.81-.56-1.22-.84l.02.02ZM49.67,53.74l.06.08-.09-.06c-.17-.17-.33-.33-.5-.5-.12-2.62-.09-5.2,1.49-7.48l.08-.05-.06.08c-3.69,1.42-4.29,4.42-1.49,**********.33.33.5.5ZM72.01,72.18l-.49.49h-.01c.16-.18.33-.34.49-.51.17-.17.34-.34.5-.5.33-.16.66-.33.99-.49h.01c-.33.18-.66.34-.99.51-.17.17-.33.34-.5.5ZM59.59,56.24h-.01c-.17-.15-.33-.32-.5-.48-.17-.17-.33-.33-.5-.5-.17-.7-.33-1.4-.51-2.17h-2.3c-2.28,2.71-4.21,3.43-6.3,2.13-.86-.53-1.66-1.39-2.12-2.3-1.07-2.09-.86-4.27.4-6.48-2.13-.17-3.68-.86-3.97-2.96-.27-2,1.16-2.69,2.67-3.36.32-.14.4-.82.8-1.7-1.17.35-1.84.43-2.37.74-5.12,2.96-9.28,6.83-11.13,12.65-.75,2.34-1.95,4.85-1.7,7.15,1.16,10.83,7.18,17.4,17.66,20.06,3.75.95,7.76.84,11.89,1.23v-4.48c.94-.56,2.02-1.21,3.14-1.88-.72-1.37-1.37-2.1-2.65-1.2l-.02-.02.22,1.14c-.21.05-.42.1-.63.15-.18-.66-.36-1.33-.6-2.19,2.93-.34,1.33-3.26,2.94-4.77.28.84.43,1.27.57,1.71l-.08.07.06-.09c.62-.05,1.25-.09,1.64-.12v-3.29h7.97c-2.17-.93-4.62-.97-5.64-3.55.07-.11.14-.21.12-.18.01-.03-.04.09-.1.21-1.05-2.48,1.4-3.53,2.22-5.18-2.41-3.14-2.35-3.98.27-4.8,0,.17,0,.34,0,.52-1.15.41-1.12,1.12-.5,1.99,1.44,1.55,2.69,1.85,3.74.87,1.03-.96.8-2.21-.71-4.19-.87.29-1.71.57-2.55.85-.11-.54-.22-1.07-.34-1.62.79-.52,1.46-.95,2.15-1.41-.19-.51-.31-.9-.47-1.27-.1-.21-.28-.39-.41-.57-2.36,1.86-2.96,1.79-4.26-.39.39-1.05.77-2.1,1.14-3.09-1.1-.81-1.99-1.47-3.08-2.27-.07.71-.12,1.19-.17,1.66-.33.33-.66.66-1,.99-2.24.17-4.47.33-6.55.49,2.56,2.42,4.17,2.57,6.55.49.27.02.76.08.76.06.1-.68.15-1.37.21-2.06,1.8,2.72-1.55,4.25-1.36,6.6-.29.24-.58.48-.87.72,1.02.61,2.03,1.23,3.18,1.92-.89.74-1.62,1.36-2.35,1.97,2.75,3.03,2.51,4.46-1.13,5.61-1.18-2.37-4.94-1.96-5.79-5.31.6.4,1,.67,1.4.93l.5.5.49.5ZM70.03,70.2c-.33.16-.66.33-1,.49l-1.74.53c.08.17.17.34.25.51.49-.35.98-.7,1.47-1.06l1-.49.02.02ZM49.15,44.29c.38-.05.77-.1,1.15-.15l-.1-.34c-.34.17-.69.34-1.03.51l-.08.05s.06-.08.06-.08ZM56.6,39.81c.58-.54,1.16-1.09,1.74-1.63-.18-.15-.37-.29-.55-.44-.39.7-.78,1.4-1.17,2.1-.18,0-.35,0-.53,0,.05-.73.11-1.46.16-2.19-.25-.03-.49-.05-.74-.08-.12.84-.25,1.68-.42,2.84.61-.36.82-.48,1.02-.6.05.12.1.25.14.37.11-.12.22-.25.33-.37ZM55.11,49.79c-.05-.13-.11-.25-.09-.22-.02-.04.05.08.12.2.36,1.41,1.17,2.23,2.74,2.06-.92-.68-1.85-1.36-2.77-2.04ZM57.6,41.3c.96-.66,1.91-1.32,2.87-1.98-.16-.2-.33-.39-.49-.59-.78.86-1.57,1.73-2.35,2.59l-.09.06s.07-.08.07-.08ZM69.03,45.78c-.2.22-.41.44-.66.72q1.93.62,1.84-1.63c-.46.35-.83.64-1.21.94l.02-.02ZM54.15,41.3c.14.54.17,1.15.47,1.58.1.15.84-.13,1.29-.21.02-.23.05-.47.07-.7-.62-.21-1.24-.43-1.86-.64-.09-1.1-.19-2.21-.28-3.31-.21.03-.42.07-.62.1-.53,1.28-1.15,2.58.93,3.18ZM56.6,48.3l-.06-.08.08.06c.37,1.88,1.97,2.24,3.45,2.88.67-.3,1.34-.6,2.01-.89-.33,1.41.37,2.06,2.21,2.52-.98-1.09-1.61-1.79-2.23-2.49-1.82-.66-3.64-1.32-5.46-1.99ZM52.63,54.25c.53-.57,1.07-1.14,1.59-1.7-2.04-2.09-2.41-2.1-3.79-.2.51-.04.86-.07,1.21-.09.17,0,.33,0,.5,0,1.28.39.81,1.22.51,2.02-.38.05-.76.1-1.14.15l.1.33c.34-.17.68-.33,1.03-.5ZM69.51,76.67c-.04-.12-.08-.24-.03-.08-.12-.13-.03-.04.05.06.77,1.37,1.9,1.31,2.84.4.79-.77.5-1.7-.56-2.17-.23-.1-.6.13-1.03.24.19.56.34,1,.59,1.78-.83-.1-1.35-.16-1.87-.22ZM61.07,41.33l.02-.02c-.32.39-.63.78-1.04,1.28,2.62.23,4.05-.21,4.3-1.32.28-1.22-.46-1.59-1.88-1.63-.37.45-.89,1.07-1.4,1.69ZM65.56,75.67c-2.48,1.44-2.78,3.75-2.37,6.25.38,2.34,2.76,3.71,4.8,2.92,1.42-.55,2.22-1.92,1.72-3.1-.52-1.23-1.51-1.83-2.78-1.36-1.61.59-1.37,1.83-.64,3.15-2.15-1.23-.77-2.7-.76-4.07v-3.8l.02.02ZM71.47,61.11c.1-.19.21-.38.31-.58.71.19,1.41.38,2.12.57.16-.17.32-.34.48-.51-.28-.64-.4-1.58-.89-1.84-.65-.35-1.58-.18-2.32-.23-.29,1.27-.49,2.13-.68,2.95,2,2.38,3.19,2.61,4.4,1.02-1.09-.44-2.25-.91-3.42-1.38ZM61.58,55.03c.57-2.55.13-3.08-2.28-2.82-.31,1.85,1.1,2.16,2.28,2.82ZM46.95,41.49c-1.68.3-1.87,1.22-1.58,2.4.03.13.2.23.36.41.13-.55.24-1.03.35-1.51.68.21,1.2.37,1.72.54.1-.14.2-.29.3-.43-.41-.5-.81-1-1.15-1.41ZM51.01,40.22c.31-.21.62-.42.93-.63-.49-.71-.97-1.42-1.46-2.14-.3.2-.59.4-.89.59.47.72.94,1.45,1.41,2.17ZM61.13,47.54c.01.19.02.39.03.58.55-.05,1.1-.08,1.64-.16.04,0,.04-.27.08-.61-.61.06-1.19.12-1.76.18ZM63.43,57.7c.21.05.43.1.64.15.02-.36.04-.73.06-1.09-.15-.02-.31-.03-.46-.05-.08.33-.17.66-.25.99ZM65.61,70.91v-.49c-.24.07-.46.14-.68.21.02.09.05.19.07.28h.61ZM52.01,42.61c.06-.06.12-.13.17-.19-.14-.13-.28-.26-.43-.39-.06.06-.12.13-.18.19.14.13.28.26.43.39ZM50.97,48.33c-.06.03-.12.06-.19.08.07.18.14.35.21.53.08-.04.16-.07.24-.11-.09-.17-.17-.34-.26-.51ZM75.33,57.08c-.06-.12-.13-.23-.19-.35-.09.1-.19.2-.26.31-.02.03.08.18.11.18.12-.03.23-.09.35-.14ZM67.61,67.43c.12.01.24.02.35.03-.12-.01-.24-.02-.35-.03Z" /><path d="M30.22,28.33h7.07c0-.2,0-.4,0-.6h-11.18c-.5-1.28-.95-2.45-1.63-4.21,3.18,0,5.76-.04,8.34.02,1.23.02,2.46.31,3.69.32,8.77.04,17.54.17,26.3-.02,4.35-.1,8.7-.73,13.03-1.29,3.46-.45,5.93.71,6.97,3.64-.57,2.23-2.53,2.44-4.28,2.67-3.67.47-7.48.29-11.03,1.2-4.57,1.18-9.03.16-13.54.3-4.94.15-9.9.15-14.84-.04-3-.11-5.98-.7-8.98-1.08.03-.3.05-.6.08-.89ZM44.69,28.04c0,.06,0,.12,0,.18h2.51c0-.06,0-.12,0-.18h-2.51Z" /><path d="M177.28,68.51h-2.34v-21.37c.48-.16.78-.34,1.09-.35.49-.03.98.05,1.85.12,3.44,5.52,6.96,11.18,10.5,16.84v-16.73c.82-.06,1.63-.12,2.68-.19v21.51c-1.92.84-3.12.26-4.07-1.34-2.98-5.05-6-10.07-9.01-15.1-.07-.12-.25-.16-.7-.43v17.05Z" /><path d="M106.56,56.99c3.55,1.85,4.22,3.41,3.59,7.25-.4,2.43-3.37,4.39-6.46,4.51-2.13.08-4.26.02-6.66.02v-21.04c2.58-1.84,9.51-1.21,11.49,1.16,2.07,2.47,1.65,6.07-1.96,8.12ZM100.04,66.32c2.82.66,5.35.42,6.69-1.82.65-1.09.13-3.22-.47-4.61-.62-1.42-4.04-1.79-6.21-1.02v7.45ZM100.11,55.8c2,.72,3.63.32,5.14-.67,1.85-1.21,2.02-4.16.17-5.22-1.52-.88-3.27-1.54-5.31-.51v6.4Z" /><path d="M140.22,46.86h3.43c2.44,7.16,4.86,14.25,7.39,21.68-1.13.07-1.9.12-2.85.17-.91-2.32-1.77-4.52-2.66-6.77h-7.17c-.83,2.13-1.69,4.33-2.57,6.59h-2.9c2.5-7.39,4.89-14.46,7.34-21.68ZM138.88,59.48h5.96c-1-3.15-1.92-6.06-3-9.47-1.08,3.45-1.98,6.33-2.96,9.47Z" /><polygon points="68.02 77.64 67.96 77.72 68.05 77.66 68.02 77.64" /><path d="M67.78,82.55c0-.12,0-.25,0-.37.04.06.12.12.11.19,0,.06-.08.12-.12.18Z" /><path d="M71,53.78c0-.51,0-1.03,0-1.54,0,.51,0,1.03,0,1.54Z" /><path d="M70.5,54.27c.18-.18.36-.36.53-.54-.18.18-.36.36-.53.54Z" /><path d="M64.54,43.78c0,.35,0,.69,0,1.04,0-.35,0-.69,0-1.04Z" /><path d="M52.16,52.28c-.18,0-.36,0-.54,0,.18,0,.36,0,.54,0Z" /><polygon points="69.01 45.8 69.11 45.87 69.03 45.78 69.01 45.8" /></g></g></svg>
              </div>
              <h1 className='text-3xl font-serif uppercase text-center text-[#1a1a1a] dark:text-[#FFFFE3]'>Ôban – Wine & Champagne Cellar</h1>
              <div className="text-xs text-center italic opacity-70 pt-4">Prices are in Moroccan dirhams</div>
            </Dialog.Title>

            <div className="max-h-[75vh] overflow-y-auto space-y-14 px-4 md:px-16">

              {/* Reusable section generator */}
              {[
                {
                  title: "Moroccan White / Blanc",
                  items: [
                    ["S de Siroua, Chardonnay – Meknès", 440],
                    ["Domaine Sahari, Grenache – Meknès", 330],
                    ["Terres Blanches, Famille Ferme Rouge", 385],
                    ["Terres Sauvages, Famille Ferme Rouge", 330],
                    ["C B Signature, Domaine Ouled Taleb", 440],
                    ["Château Roslane, Couteaux d’Atlas", 440],
                    ["Aït Souala Domaine Ouled Taleb", 385],
                    ["El Mogador Domaine Ouled Taleb", 385],
                    ["C B Initiales Domaine Ouled", 440],
                    ["Médaillon Sauvignon", 385],
                    ["Volubilia", 385],
                    ["Glass of House White", 70],
                  ],
                },
                {
                  title: "Moroccan Rosé",
                  items: [
                    ["Terres Rosé. La Ferme Rouge", 385],
                    ["Médaillon Syrah", 385],
                    ["Volubilia", 385],
                    ["La Perle de Mogador", 385],
                  ],
                },
                {
                  title: "Moroccan Gris / Blush",
                  items: [
                    ["La Ferme rouge – Le Gris", 385],
                    ["Médaillon Gris", 385],
                    ["Volubilia", 385],
                    ["La Perle de Mogador Gris", 385],
                    ["Glass of House Rosé / Blush", 70],
                  ],
                },
                {
                  title: "Moroccan Red / Rouge",
                  items: [
                    ["Sahari Réserve Grenache Meknès", 330],
                    ["Médaillon Cabernet (2014) Meknès", 385],
                    ["Terres Rouge Domaine La Ferme Rouge", 385],
                    ["Volubilia Classic Domaine Zouina (2014)", 385],
                    ["C B Initiales Domaine de Ouleb Taleb (2014)", 385],
                    ["C B Signature Domaine de Oleb Taleb (2014)", 385],
                    ["El Mogador Domaine de Ouled Taleb (2014)", 385],
                    ["Eclipse Grenache/Syrah AO Guerrouane (2014)", 440],
                    ["Aït Souala Domaine de Ouleb Taleb (2014)", 440],
                    ["Epicuria Syrah Domaine Zouina Meknès (2013)", 550],
                    ["Epicuria Cabernet Domaine Zouina Meknès (2013)", 550],
                    ["Lumiere Grenache Domaine Guerrouane (2014)", 660],
                    ["Château Roslane Les Couteaux d’Atlas (2012)", 660],
                    ["Tandem Domaine de Oleb Taleb (2012)", 660],
                  ],
                },
                {
                  title: "French White / Blanc",
                  items: [
                    ["Pithon Pallé, Quatre Vents (2013)", 660],
                    ["Meursault, Dom. Decelle-villa (2012)", 2145],
                    ["La Galopine, M. Delas Condrieu (2012)", 2145],
                    ["Puligny Montrachet, Dom. Boillot (2013)", 2585],
                    ["Sancerre, Domaine Vincent Pinard (2014)", 1100],
                    ["Sancerre, Domaine Vincent Pinard (2014)", 660],
                    ["Château de l’Ou, Côte de Rossillon (2015)", 550],
                    ["Domaine de l’Herré, Côte de Gascogne (2015)", 330],
                    ["Château de la Grande Métairie, Bordeaux (2015)", 440],
                    ["Mâcon Prissé, Domaine des Valange (2013)", 550],
                    ["Le Petit Chenin, Château de la Roulerie (2015)", 440],
                    ["Vieilles Vignes, Ribeauville Alsace (2012)", 495],
                    ["Beaune 1er Cru, Jean Marc Boillot (2013)", 1540],
                    ["La Curieuse, De Raissac Libron (2002)", 1100],
                    ["Corton Charlemagne, Decelle-villa (2014)", 4400],
                    ["Chassagne-Montrachet, Beaune (2013)", 2145],
                    ["Domaine Cauhapé, Sud-Ouest (2012)", 660],
                    ["La Cadène, Côte du Rhône (2013)", 770],
                  ],
                },
                {
                  title: "French Rosé",
                  items: [
                    ["R de Robine, C. de Provence (2014)", 440],
                    ["La Comballe Delas, Val du Rhône (2012)", 550],
                    ["Petale de Rose, Tour de l’Evêque (2014) 0,75l", 550],
                    ["Château des Marres Prestige, Côte de Provence (2013)", 440],
                  ],
                },
                {
                  title: "French Red / Rouge",
                  items: [
                    ["Chat. Lamothe Castéra (2012)", 550],
                    ["Vinsobres, Domaine Voillot (2013)", 770],
                    ["Crôzes Hermitage, D. Combier (2012)", 990],
                    ["Saumur Champigny, Thierry Germain (2014)", 660],
                    ["Crôzes Bages, Petite Ruche – Mr. Chapoutier (2013)", 990],
                    ["Chambertin, Château Latricière – Grand Cru (2011)", 1450],
                    ["Gevrey Chambertin, Albert Bichot – Côte d’Or (2010)", 2200],
                    ["Château Clos la Rosh, JC Boisset – Grand Cru (2005)", 2750],
                    ["Château Chasse Spleen, Moulis en Medoc (2003)", 2200],
                    ["Château Clarke, Baron Edmond (2008)", 1100],
                    ["Attitude Pinot Noir (2013)", 550],
                    ["Croizet Bages (2009)", 2750],
                    ["Brouilly Beaujolais (2014)", 4400],
                    ["Hauts de St Estèphe (2011)", 1100],
                    ["Chateau Mouton Rothschild (1990)", 8800],
                    ["Château la Croix d’Antonne (2012)", 2800],
                    ["Clos Cheval Blanc, Côtes de Bourg (2011)", 4400],
                    ["Beaucastel, Chateauneuf-du-Pape (2012)", 2850],
                    ["Château Beychevelle St Julien (2011)", 5500],
                    ["Nuit St George, Domaine Predix.", 1850],
                    ["Chateau Angelus (2011)", 9900],
                    ["Chateau de l’Ou", 550],
                  ],
                },
                {
                  title: "Spanish Red",
                  items: [
                    ["Romondo, Vendima (2014)", 550],
                    ["Montessa, Pal.Romondo (2014)", 770],
                    ["Petalos Bierzo - Desc. J. Palacios (2013)", 880],
                    ["Lusto Amontillado, Arcos-Xéres (2013)", 880],
                    ["Lusto Jarana Fino - Xéres", 880],
                    ["Lusto Vermut Rouge", 880],
                  ],
                },
                {
                  title: "Italian Red",
                  items: [
                    ["Guest di Nostri (2012)", 2200],
                    ["Barolo Briccotto 150cl (2008)", 8800],
                  ],
                },
                {
                  title: "Champagne Brut",
                  items: [
                    ["Mumm Cordon Rouge", 1100],
                    ["Mumm Sylver", 1600],
                    ["Laurent Perrier", 2200],
                    ["Möet Chandon", 2200],
                    ["Deutz", 2800],
                    ["Perrier Jouet Grand Brut", 3000],
                    ["Bollinger", 3300],
                    ["Mumm Blanc de Blancs", 3000],
                    ["Perrier-Jouët Belle Epoque", 5500],
                    ["Möet & Chandon, Dom Pérignon", 5500],
                  ],
                },
                {
                  title: "Champagne Rosé",
                  items: [
                    ["Olivier Collin", 1850],
                    ["Mumm Cordon Rouge", 2600],
                    ["Deutz", 2600],
                    ["Perrier Jouet Blason Rosé", 3000],
                    ["Bollinger", 3300],
                    ["Möet & Chandon", 4400],
                    ["Perrier Jouët Belle Epoque", 7500],
                    ["Laurent Perrier", 3300],
                    ["Glass of Champagne", 180],
                  ],
                },
              ].map(({ title, items }) => (
                <div key={title}>
                  <h2 className="text-center text-xl font-semibold uppercase mb-6 tracking-wider">{title}</h2>
                  <ul className="space-y-3">
                    {items.map(([name, price]) => (
                      <li key={name} className="flex justify-between">
                        <span>{name}</span>
                        <span>{price}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}

              <div className="text-sm text-center italic opacity-70 pt-4">
                Prices are in Moroccan dirhams
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>


    </div>
  );
}
