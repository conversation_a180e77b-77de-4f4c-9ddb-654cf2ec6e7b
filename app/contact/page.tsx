'use client';

import { AnimatedText } from '@/components/ui/animated-text';
import { Button } from '../../components/ui/button';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, <PERSON>readcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { FadeInWhenVisible } from '@/components/ui/fade-in-when-visible';

export default function ContactPage() {
  return (
    <div className="text-primary min-h-screen pb-20">

      {/* Parallax Hero with Text */}
      <section
        className="relative h-[55vh] w-full bg-fixed bg-center bg-cover flex items-center justify-center px-6 text-center"
        style={{
          backgroundImage: "url('/images/rooms/junior_suite_renard_des_sables.jpg')",
        }}
      >
        <FadeInWhenVisible delay={0}>
          <div className="bg-black/60 p-6 rounded-xl text-center max-w-3xl mx-auto">
            <div className='w-fit mx-auto'>
              <h1 className="text-4xl md:text-5xl font-serif uppercase text-[#FFFFE3] tracking-wider mb-4">Contact</h1>
              <svg id="a" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 208.33 7.34" className="w-80 text-[#FFFFE3] mx-auto my-3 block align-middle leading-none" fill="currentColor"><path d="M104.43,2.94c-1.86-1.1-3.91-1.79-6.01-2.13-2.11-.33-4.26-.25-6.38-.05-4.25.47-8.39,1.61-12.47,2.92l-6.13,2c-2.04.63-4.13,1.34-6.35,1.57-4.38.48-9.05-.73-12.37-3.92l.41.18-13.65-.04c-4.55-.03-9.1-.01-13.65-.07l-13.65-.16c-4.55-.1-9.63-.15-14.18-.28,4.55-.13,9.63-.19,14.18-.28l13.65-.16c4.55-.06,9.1-.05,13.65-.07l13.65-.04c.15,0,.29.06.39.16l.03.03c1.4,1.39,3.3,2.48,5.27,3.07,1.98.62,4.09.81,6.17.6,2.08-.2,4.1-.86,6.16-1.48,2.05-.64,4.11-1.28,6.17-1.88,4.13-1.21,8.35-2.29,12.67-2.64,2.16-.12,4.34-.11,6.48.3,2.12.42,4.17,1.22,5.96,2.42Z" /><path d="M117.55,2.84c1.79-1.18,3.85-1.96,5.97-2.33,2.12-.39,4.3-.43,6.45-.29,4.31.31,8.52,1.38,12.65,2.53l6.17,1.81c2.06.61,4.09,1.23,6.16,1.38,4.13.44,8.41-.54,11.88-3.09-1.39,1.68-3.36,2.81-5.44,3.53-2.1.66-4.36.89-6.55.65-2.2-.24-4.29-.95-6.33-1.55l-6.13-1.93c-4.08-1.25-8.22-2.37-12.46-2.8-4.2-.45-8.66-.11-12.38,2.1Z" /><path d="M104.43,2.94c1.79-1.2,3.84-2,5.96-2.42,2.13-.42,4.32-.42,6.48-.3,4.33.35,8.54,1.43,12.67,2.64l6.17,1.88c2.06.62,4.08,1.28,6.16,1.48,2.08.21,4.19.02,6.17-.59,1.97-.59,3.87-1.68,5.27-3.07l.02-.02c.11-.11.25-.16.39-.16l13.97.04,13.97.07,12.68.16c4.66.09,9.31.15,13.97.28-4.66.13-9.32.19-13.97.28l-12.68.16-13.97.07-13.97.04.41-.18c-3.31,3.19-7.98,4.4-12.37,3.92-2.21-.23-4.31-.94-6.35-1.57l-6.13-2c-4.08-1.31-8.22-2.45-12.47-2.92-2.12-.2-4.27-.27-6.38.06-2.1.35-4.15,1.03-6.01,2.14Z" /><path d="M43.87,2.84c1.64,1.38,3.6,2.33,5.64,2.82,2.04.54,4.17.65,6.24.39,2.08-.26,4.09-.9,6.14-1.53l6.14-1.91c4.13-1.17,8.34-2.25,12.68-2.56,2.16-.1,4.35-.09,6.49.33,2.12.46,4.18,1.24,5.95,2.46-1.9-1-3.95-1.64-6.04-1.91-2.1-.3-4.23-.21-6.33-.01-2.1.26-4.19.61-6.27,1.09-2.07.5-4.12,1.05-6.17,1.68l-6.16,1.81c-2.05.58-4.15,1.27-6.35,1.41-2.18.17-4.4-.05-6.48-.72-1.04-.33-2.03-.79-2.96-1.35-.93-.55-1.81-1.2-2.53-2Z" /><path d="M7,1.78c-1.43,1.05-4.47,1.16-4.47,1.16,0,0,3.05.11,4.47,1.16,1.43,1.05,2.32-.24,1.67-1.16.65-.92-.24-2.22-1.67-1.16Z" /><path d="M2.94,2.94c0,.39-.32.71-.71.71s-.71-.32-.71-.71.32-.71.71-.71.71.32.71.71Z" /><path d="M201.32,4.11c1.43-1.05,4.48-1.16,4.48-1.16,0,0-3.05-.11-4.48-1.16-1.43-1.05-2.32.24-1.66,1.16-.66.92.23,2.22,1.66,1.16Z" /><path d="M205.39,2.94c0-.39.32-.71.71-.71s.71.32.71.71-.32.71-.71.71-.71-.32-.71-.71Z" /></svg>
            </div>
            <AnimatedText text='Riad Noir d’Ivoire is a boutique hotel hidden inside the Medina.
            Beautiful decor, personalized service and a passion for guest satisfaction
            make it a perfect retreat. We have a heated pool, a small gym and many cozy areas
            within the property to unwind and relax. Free Wi-Fi is available throughout the Riad.' className="text-[#FFFFE3] text-lg leading-relaxed font-light tracking-wide mt-2 leading-snug" />
          </div>
        </FadeInWhenVisible>
      </section>

      {/* Contact Section */}
      <div className="container mx-auto px-4 mt-20 lg:px-30 xl:px-50">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Contact</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Map & Contact Info */}
        <FadeInWhenVisible delay={0}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-20 justify-center items-center mt-20">

            {/* Map placeholder */}
            <div className="relative bg-gradient-to-br bg-secondary dark:bg-[#1a1a1a] rounded-2xl  h-full shadow-2xl border dark:border-[#ffffe3]/20">
              <iframe
                title="Noir D'ivoire location"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3397.0091537972876!2d-7.997817085090785!3d31.633599748707812!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xdafee41aea941ff%3A0xd5d687fc366ae650!2sRiad+Noir+D'Ivoire!5e0!3m2!1sen!2sus!4v1535120205919"
                className='rounded-lg w-full h-full'
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              ></iframe>
            </div>

            {/* Contact Info */}
            <div className="relative bg-gradient-to-br bg-secondary dark:bg-[#1a1a1a] rounded-2xl p-8 shadow-2xl border border-black/20 dark:border-[#ffffe3]/20">
              <div className="absolute top-4 right-6 text-3xl text-black dark:text-[#d0b77f] opacity-20">✦</div>
              <h2 className="text-2xl font-serif uppercase text-center text-[#333] text-black dark:text-primary mb-4 tracking-wide">
                Riad Noir d’Ivoire
              </h2>
              <div className="space-y-4 text-center text-black dark:text-primary text-[15px] leading-relaxed">
                <p>
                  <span className="text-[#d0b77f] mr-2">📍</span>
                  Bab Doukkala, Marrakech, Morocco
                </p>
                <p>
                  <span className="text-[#d0b77f] mr-2">📞</span>
                  +212 5 24 38 76 45
                </p>
                <p>
                  <span className="text-[#d0b77f] mr-2">📧</span>
                  <EMAIL>
                </p>
              </div>
            </div>
          </div>
        </FadeInWhenVisible>

        {/* Contact Form */}
        <FadeInWhenVisible delay={0}>
          <div className="bg-secondary dark:bg-[#1a1a1a] rounded-xl p-8 shadow-2xl max-w-4xl mx-auto border border-black/20 dark:border-[#ffffe3]/20">
            <h3 className="text-2xl font-serif text-black dark:text-primary mb-6 text-center">
              Send us a message
            </h3>
            <p className="text-center text-black dark:text-primary opacity-80 mb-8">
              We would love to hear from you.
            </p>

            <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Title */}
              <div>
                <label className="block mb-1 text-lg font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">Title</label>
                <select className="w-full p-3 rounded-lg border border-gray-300 dark:border-neutral-700 bg-secondary dark:bg-[#2a2a2a] text-black dark:text-primary focus:outline-none focus:ring-2 focus:ring-[#d0b77f]">
                  <option>Mr</option>
                  <option>Mrs</option>
                  <option>Ms</option>
                </select>
              </div>

              {/* Last Name */}
              <div>
                <label className="block mb-1 text-lg font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">Last Name</label>
                <input type="text" placeholder="Your last name" className="w-full p-3 rounded-lg border border-gray-300 dark:border-neutral-700 bg-secondary dark:bg-[#2a2a2a] dark:text-[#FFFFE3] dark:text-[#FFFFE3] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[#d0b77f]" />
              </div>

              {/* First Name */}
              <div>
                <label className="block mb-1 text-lg font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">First Name</label>
                <input type="text" placeholder="Your first name" className="w-full p-3 rounded-lg border border-gray-300 dark:border-neutral-700 bg-secondary dark:bg-[#2a2a2a] text-[#FFFFE3] dark:text-[#FFFFE3] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[#d0b77f]" />
              </div>

              {/* Country */}
              <div>
                <label className="block mb-1 text-lg font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">Country</label>
                <input type="text" placeholder="Your country" className="w-full p-3 rounded-lg border border-gray-300 dark:border-neutral-700 bg-secondary dark:bg-[#2a2a2a] text-[#FFFFE3] dark:text-[#FFFFE3] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[#d0b77f]" />
              </div>

              {/* Phone */}
              <div>
                <label className="block mb-1 text-lg font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">Phone</label>
                <input type="tel" placeholder="Your phone number" className="w-full p-3 rounded-lg border border-gray-300 dark:border-neutral-700 bg-secondary dark:bg-[#2a2a2a] text-[#FFFFE3] dark:text-[#FFFFE3] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[#d0b77f]" />
              </div>

              {/* Email */}
              <div>
                <label className="block mb-1 text-lg font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">Email</label>
                <input type="email" placeholder="Your email address" className="w-full p-3 rounded-lg border border-gray-300 dark:border-neutral-700 bg-secondary dark:bg-[#2a2a2a] text-[#FFFFE3] dark:text-[#FFFFE3] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[#d0b77f]" />
              </div>

              {/* Message (full width) */}
              <div className="md:col-span-2">
                <label className="block mb-1 text-lg font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">Message</label>
                <textarea rows={6} placeholder="Your message" className="w-full p-3 rounded-lg border border-gray-300 dark:border-neutral-700 bg-secondary dark:bg-[#2a2a2a] text-[#FFFFE3] dark:text-[#FFFFE3] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[#d0b77f]" />
              </div>

              {/* Submit Button (centered) */}
              <div className="md:col-span-2 flex justify-center">
                <Button type="submit" className="hover:bg-[#FFFFE3] cursor-pointer hover:text-black font-semibold py-3 px-8 rounded-lg transition">
                  Submit
                </Button>
              </div>
            </form>
          </div>
        </FadeInWhenVisible>
      </div>
    </div>
  );
}
