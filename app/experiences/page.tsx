'use client';

import { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { X } from 'lucide-react';
import Image from 'next/image';
import { Lens } from '@/components/ui/lens';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { Carousel } from '@/components/ui/apple-cards-carousel';
import { motion } from 'motion/react';
import { Button } from '@/components/ui/button';
import { AnimatedText } from '@/components/ui/animated-text';
import { FadeInWhenVisible } from '@/components/ui/fade-in-when-visible';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

const offers = [
  {
    id: 'romance-experience',
    title: 'Romance Experience',
    image: '/images/experiences/romance.webp',
    description: 'Treat yourself and a loved one to an unforgettably romantic stay, guaranteed to make you feel extra special!',
    details: `Treat yourself and a loved one to an unforgettably romantic stay, guaranteed to make you feel extra special!
• Price: 4150 MAD for 2 people
• Fast track service at Marrakech airport on arrival
• Airport transfer from Marrakech airport on arrival
• Royal Hammam using exotic Moroccan oils and rose petals (1hr 15mins)
• ‘Caleche’– horse drawn carriage around Marrakech in the evening`
  },
  {
    id: 'discovery-experience',
    title: 'Discovery Experience',
    image: '/images/experiences/discovery.webp',
    description: 'Explore the ‘Red City’ and it’s rich culture! The perfect way to discover Marrakech!',
    details: `Explore the ‘Red City’ and it’s rich culture! The perfect way to discover Marrakech!
• Price: 3850 MAD for 2 people
• Airport transfer from Marrakech airport on arrival
• Guided walking tour of ancient Marrakech Medina (4 hours with an official guide in your chosen language)
• Traditional Moroccan Hammam (45 mins)
• Traditional Moroccan Dinner
• Camel ride in the Palmeraie area (1hr)`
  },
  {
    id: 'gastronomy-experience',
    title: 'Gastronomy Experience',
    image: '/images/experiences/gastronomy.webp',
    description: 'Learn and experience first-hand the rich gastronomical culture of Morocco in this food lover’s experience.',
    details: `Learn and experience first-hand the rich gastronomical culture of Morocco in this food lover’s experience.
• Price: 3200 MAD for 2 people
• Arrival airport transfer from Marrakech airport
• Traditional Moroccan dinner on first night
• Mixology class at Charlie’s Bars
• Moroccan wine master class and bottle of Moroccan wine to share and enjoy`
  },
  {
    id: 'golf',
    title: 'Golf',
    image: '/images/experiences/golf.webp',
    description: 'Play and relax, let us take care of both! Play on two of Morocco’s best courses and return to relax with a sublime massage.',
    details: `Play and relax, let us take care of both! Play on two of Morocco’s best courses and return to relax with a sublime massage.
• Price: 6400 MAD for 2 people
• Fast track service at Marrakech airport on arrival
• Arrival airport transfer from Marrakech airport
• Two 18 hole rounds at Royal Golf and Assoufid Golf
• Return transfers to both golf courses
• Relaxing massage in Coco’s Spa`
  }
];

function CarouselCard({ offer, onClick }: { offer: (typeof offers)[number]; onClick: () => void }) {
  const [hovering, setHovering] = useState(false);

  return (
    <div
      key={offer.id}
      className="relative h-full min-h-[20rem] w-full min-w-[calc(100vw-30vw)] md:min-w-[calc(100vw-50vw)] lg:min-w-[calc(100vw-70vw)] xl:min-w-[calc(100vw-80vw)] rounded-2xl p-2 md:rounded-3xl md:p-3"
    >
      <GlowingEffect spread={40} glow={true} disabled={false} proximity={64} inactiveZone={0.01} />
      <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border dark:shadow-[0px_0px_15px_0px_#FFFFE3] shadow-[0px_0px_15px_0px_#00000050] p-6 dark:border-0 dark:shadow-[0px_0px_27px_0px_#2D2D2D]">
        <div className="relative z-10 flex flex-col flex-1">
          <Lens hovering={hovering} setHovering={() => setHovering(!hovering)}>
            <Image
              src={offer.image}
              alt={offer.title}
              height={300}
              width={300}
              style={{ objectFit: 'cover', objectPosition: 'center' }}
              className="rounded-2xl w-full h-60 select-none"
              loading="eager"
            />
          </Lens>
          <div className="flex flex-col justify-between flex-1">
            <motion.div className="py-4 relative z-20">
              <h2 className="text-black dark:text-white text-2xl text-left font-bold select-none">{offer.title}</h2>
              <AnimatedText text={offer.description} className="text-neutral-800 dark:text-neutral-300 text-left mt-4 select-none" />
            </motion.div>
            <motion.div className="py-4 relative z-20">
              <Button
                onClick={onClick}
                className="text-sm font-semibold py-2 px-5 rounded-full border border-white hover:bg-[#FFFFE3] hover:text-black transition cursor-pointer select-none"
              >
                Learn More
              </Button>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function OffersPage() {
  const [activeOffer, setActiveOffer] = useState<null | string>(null);

  return (
    <div className="min-h-screen bg-secondary dark:bg-[#0e0e0e] text-[#1a1a1a] dark:text-[#FFFFE3]">
      <section
        className="relative h-[65vh] bg-fixed bg-center bg-cover flex items-center justify-center"
        style={{ backgroundImage: "url('/images/sliders/12.webp')" }}
      >
        <FadeInWhenVisible delay={0}>
          <div className="bg-black/60 p-6 rounded-xl text-center max-w-3xl mx-auto">
            <div className='w-fit mx-auto'>
              <h1 className="text-4xl md:text-5xl font-serif uppercase text-[#FFFFE3] tracking-wider mb-4">Experiences</h1>
              <svg id="a" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 208.33 7.34" className="w-80 text-[#FFFFE3] mx-auto my-3 block align-middle leading-none" fill="currentColor"><path d="M104.43,2.94c-1.86-1.1-3.91-1.79-6.01-2.13-2.11-.33-4.26-.25-6.38-.05-4.25.47-8.39,1.61-12.47,2.92l-6.13,2c-2.04.63-4.13,1.34-6.35,1.57-4.38.48-9.05-.73-12.37-3.92l.41.18-13.65-.04c-4.55-.03-9.1-.01-13.65-.07l-13.65-.16c-4.55-.1-9.63-.15-14.18-.28,4.55-.13,9.63-.19,14.18-.28l13.65-.16c4.55-.06,9.1-.05,13.65-.07l13.65-.04c.15,0,.29.06.39.16l.03.03c1.4,1.39,3.3,2.48,5.27,3.07,1.98.62,4.09.81,6.17.6,2.08-.2,4.1-.86,6.16-1.48,2.05-.64,4.11-1.28,6.17-1.88,4.13-1.21,8.35-2.29,12.67-2.64,2.16-.12,4.34-.11,6.48.3,2.12.42,4.17,1.22,5.96,2.42Z" /><path d="M117.55,2.84c1.79-1.18,3.85-1.96,5.97-2.33,2.12-.39,4.3-.43,6.45-.29,4.31.31,8.52,1.38,12.65,2.53l6.17,1.81c2.06.61,4.09,1.23,6.16,1.38,4.13.44,8.41-.54,11.88-3.09-1.39,1.68-3.36,2.81-5.44,3.53-2.1.66-4.36.89-6.55.65-2.2-.24-4.29-.95-6.33-1.55l-6.13-1.93c-4.08-1.25-8.22-2.37-12.46-2.8-4.2-.45-8.66-.11-12.38,2.1Z" /><path d="M104.43,2.94c1.79-1.2,3.84-2,5.96-2.42,2.13-.42,4.32-.42,6.48-.3,4.33.35,8.54,1.43,12.67,2.64l6.17,1.88c2.06.62,4.08,1.28,6.16,1.48,2.08.21,4.19.02,6.17-.59,1.97-.59,3.87-1.68,5.27-3.07l.02-.02c.11-.11.25-.16.39-.16l13.97.04,13.97.07,12.68.16c4.66.09,9.31.15,13.97.28-4.66.13-9.32.19-13.97.28l-12.68.16-13.97.07-13.97.04.41-.18c-3.31,3.19-7.98,4.4-12.37,3.92-2.21-.23-4.31-.94-6.35-1.57l-6.13-2c-4.08-1.31-8.22-2.45-12.47-2.92-2.12-.2-4.27-.27-6.38.06-2.1.35-4.15,1.03-6.01,2.14Z" /><path d="M43.87,2.84c1.64,1.38,3.6,2.33,5.64,2.82,2.04.54,4.17.65,6.24.39,2.08-.26,4.09-.9,6.14-1.53l6.14-1.91c4.13-1.17,8.34-2.25,12.68-2.56,2.16-.1,4.35-.09,6.49.33,2.12.46,4.18,1.24,5.95,2.46-1.9-1-3.95-1.64-6.04-1.91-2.1-.3-4.23-.21-6.33-.01-2.1.26-4.19.61-6.27,1.09-2.07.5-4.12,1.05-6.17,1.68l-6.16,1.81c-2.05.58-4.15,1.27-6.35,1.41-2.18.17-4.4-.05-6.48-.72-1.04-.33-2.03-.79-2.96-1.35-.93-.55-1.81-1.2-2.53-2Z" /><path d="M7,1.78c-1.43,1.05-4.47,1.16-4.47,1.16,0,0,3.05.11,4.47,1.16,1.43,1.05,2.32-.24,1.67-1.16.65-.92-.24-2.22-1.67-1.16Z" /><path d="M2.94,2.94c0,.39-.32.71-.71.71s-.71-.32-.71-.71.32-.71.71-.71.71.32.71.71Z" /><path d="M201.32,4.11c1.43-1.05,4.48-1.16,4.48-1.16,0,0-3.05-.11-4.48-1.16-1.43-1.05-2.32.24-1.66,1.16-.66.92.23,2.22,1.66,1.16Z" /><path d="M205.39,2.94c0-.39.32-.71.71-.71s.71.32.71.71-.32.71-.71.71-.71-.32-.71-.71Z" /></svg>
            </div>
            <AnimatedText text='Marrakech is a city brimming with rich, vibrant culture offering its visitors everything from romance and adventure to sport.
Our unique experiences offer the opportunity to explore this exotic city and its hidden treasures.' className="text-[#FFFFE3] text-lg leading-relaxed font-light tracking-wide mt-2 leading-snug" />
          </div>
        </FadeInWhenVisible>
      </section>
      <section className="container mx-auto px-4 lg:px-30 xl:px-50 py-20 ">
        <Breadcrumb className='mb-10'>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Experiences</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Carousel items={offers.map((offer) => (
          <CarouselCard key={offer.id} offer={offer} onClick={() => setActiveOffer(offer.id)} />
        ))} />
      </section>

      <Dialog open={!!activeOffer} onClose={() => setActiveOffer(null)} className="relative z-50">
        <div className="fixed inset-0 bg-black/60" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4 overflow-y-auto">
          <Dialog.Panel className="max-w-3xl w-full bg-secondary dark:bg-[#1a1a1a] p-10 rounded-lg shadow-xl relative font-serif text-[#1a1a1a] dark:text-[#FFFFE3]">
            <button
              onClick={() => setActiveOffer(null)}
              className="absolute top-4 right-4 text-gray-600 hover:text-black dark:text-gray-300 dark:hover:text-neutral cursor-pointer"
            >
              <X size={24} className='cursor-pointer' />
            </button>
            {offers.map(
              (offer) =>
                offer.id === activeOffer && (
                  <div key={offer.id}>
                    <h2 className="text-3xl font-serif uppercase text-center mb-6 tracking-wider">{offer.title}</h2>
                    <p className="whitespace-pre-line leading-relaxed text-md  opacity-90 my-5">
                      {offer.details}
                    </p>
                    <a href="https://www.mews.li/distributor/b33f587b-8ffc-417a-8af2-ab5700ddd7c3" target='_blank'>
                      <Button className='cursor-pointer'>Book now →</Button>
                    </a>
                  </div>
                )
            )}
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
}
