import React from 'react';
import { Hero<PERSON>lider } from '@/components/hero-slider';
import { Testimonials } from '@/components/testimonials';
import { EssenceSection } from '@/components/essence-section';
import VideoSection from '@/components/video-section';
import ParallaxBanner from '@/components/ui/parallax-banner';
import { PartnersSection } from '@/components/partners-section';
import { FadeInWhenVisible } from '@/components/ui/fade-in-when-visible';
import { RoomCarousel } from '@/components/room-carousel';

export default function Home() {
  return (
    <div className='grid items-center justify-items-center min-h-screen font-[family-name:var(--font-geist-sans)]'>
      <main className='flex flex-col row-start-2 items-center sm:items-start w-full overflow-x-hidden'>
        <HeroSlider></HeroSlider>
        <FadeInWhenVisible delay={0.2}>
          <EssenceSection></EssenceSection>
        </FadeInWhenVisible>

        <ParallaxBanner
          src='/images/sliders/5.webp'
          href='https://www.mews.li/distributor/b33f587b-8ffc-417a-8af2-ab5700ddd7c3'
          height='h-70'
        ></ParallaxBanner>

        {/* <RoomsSection></RoomsSection> */}
        <RoomCarousel></RoomCarousel>
        <VideoSection></VideoSection>
        <FadeInWhenVisible delay={0.2}>

          <PartnersSection></PartnersSection>
        </FadeInWhenVisible>
        <ParallaxBanner
          src='/images/rooms/guepard/1.jpg'
          href='https://www.mews.li/distributor/b33f587b-8ffc-417a-8af2-ab5700ddd7c3'
          height='h-70'
        ></ParallaxBanner>

        {/* <ExperiencesSection></ExperiencesSection> */}
        <Testimonials></Testimonials>

      </main>
    </div>
  );
}
