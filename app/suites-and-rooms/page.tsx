'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Image from 'next/image';
import Link from 'next/link';
import { rooms, amenityIcons, Room } from './lib/rooms';
import { motion } from 'framer-motion';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { AnimatedText } from '@/components/ui/animated-text';
import { FadeInWhenVisible } from '@/components/ui/fade-in-when-visible';
import { Lens } from '@/components/ui/lens';
import { useState } from 'react';

function RoomCard({ room, isEven }: { room: Room; isEven: boolean }) {
  const [hovering, setHovering] = useState(false);
  const trimmedAmenities = room.amenities.slice(0, 6);
  const maxChars = 180;
  let shortDescription = room.description;

  if (room.description.length > maxChars) {
    const nextSpaceIndex = room.description.indexOf(' ', maxChars);

    if (nextSpaceIndex !== -1) {
      shortDescription = room.description.slice(0, nextSpaceIndex).trim() + '...';
    } else {
      shortDescription = room.description;
    }
  }
  return (
    <FadeInWhenVisible delay={0}>
      <motion.section
        className={`flex flex-col md:flex-row ${!isEven ? 'md:flex-row-reverse' : ''} group rounded-2xl overflow-hidden shadow-xl`}
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
        viewport={{ once: true }}
      >
        {/* Image block with Lens */}
        <div
          className={`w-full md:w-1/2 relative aspect-[16/9] overflow-hidden
    ${isEven
              ? 'rounded-t-2xl md:rounded-l-2xl md:rounded-r-none'
              : 'rounded-t-2xl md:rounded-r-2xl md:rounded-l-none'
            }`}
        >
          <Lens hovering={hovering} setHovering={setHovering} zoomFactor={1.6} lensSize={180}>
            <Image
              src={room.images[0]}
              alt={room.name}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
          </Lens>
        </div>

        {/* Content block */}
        <Card
          className={`w-full md:w-1/2 bg-muted/50 p-6 md:p-10 flex flex-col justify-between
    ${isEven
              ? 'rounded-none md:rounded-r-2xl md:rounded-l-none'
              : 'rounded-none md:rounded-l-2xl md:rounded-r-none'
            }`}
        >
          <CardContent className="p-0 space-y-6 flex flex-col h-full justify-between">
            <div>
              <h2 className="text-2xl md:text-3xl font-semibold tracking-tight">{room.name}</h2>
              <AnimatedText
                text={shortDescription}
                className="text-muted-foreground mt-2 line-clamp-3"
              />
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              {trimmedAmenities.map((amenity: string) => (
                <div key={amenity} className="flex items-center gap-2">
                  {amenityIcons[amenity] ?? <span className="w-6" />}
                  <span className="text-sm text-muted-foreground">{amenity}</span>
                </div>
              ))}
            </div>
            <div className="pt-4">
              <Button asChild>
                <Link href={`/suites-and-rooms/${room.slug}`}>Discover more</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

      </motion.section>
    </FadeInWhenVisible>
  );
}


export default function RoomsPage() {
  return (
    <FadeInWhenVisible delay={0}>
      <section className="pt-10 pb-4 px-4 md:px-8 lg:px-20 xl:px-40 2xl:px-80 w-full mt-30">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Suites & Rooms</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col md:flex-row md:items-end justify-between mb-6 mt-10">
          <div>
            <p className="text-sm uppercase tracking-widest text-black dark:text-primary font-medium mb-2">
              Discover Our Accommodations
            </p>
            <h2 className="text-4xl md:text-5xl font-serif text-black dark:text-primary leading-tight">
              Suites & Rooms
            </h2>
          </div>
        </div>

        <div className="flex flex-col gap-20 pt-5 pb-20">
          {rooms.map((room, index) => (
            <RoomCard key={index} room={room} isEven={index % 2 === 0} />
          ))}
        </div>
      </section>
    </FadeInWhenVisible>
  );
}
