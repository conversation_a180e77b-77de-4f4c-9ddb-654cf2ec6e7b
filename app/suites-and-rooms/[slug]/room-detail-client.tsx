'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import Lightbox from 'yet-another-react-lightbox';
import Zoom from 'yet-another-react-lightbox/plugins/zoom';
import 'yet-another-react-lightbox/styles.css';

import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { amenityIcons, Room } from '@/app/suites-and-rooms/lib/rooms';
import { Button } from '@/components/ui/button';
import { AnimatedText } from '@/components/ui/animated-text';
import {
    Carousel,
    CarouselApi,
    CarouselContent,
    CarouselItem,
} from '@/components/ui/carousel-room';
import { RoomCarousel } from '@/components/room-carousel';
import Link from 'next/link';
import { Lens } from '@/components/ui/lens';
import { useTranslations } from 'next-intl';

interface Props {
    room: Room;
}

export default function RoomDetailClient({ room }: Props) {
    const t = useTranslations('room-detail');
    const [activeIndex, setActiveIndex] = useState(0);
    const [isLightboxOpen, setIsLightboxOpen] = useState(false);
    const [carouselApi, setCarouselApi] = useState<CarouselApi>();

    useEffect(() => {
        if (!carouselApi) return;

        const updateSelection = () => {
            setActiveIndex(carouselApi.selectedScrollSnap());
        };

        updateSelection();
        carouselApi.on('select', updateSelection);

        return () => {
            carouselApi.off('select', updateSelection);
        };
    }, [carouselApi]);

    return (
        <>
            <section className="pb-4 px-4 md:px-8 lg:px-20 xl:px-40 2xl:px-80 w-full py-40 min-h-screen">
                {/* Breadcrumbs */}
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/">Home</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbLink href="/suites-and-rooms">Suites & Rooms</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator />
                        <BreadcrumbItem>
                            <BreadcrumbPage>{room.name}</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>

                {/* Room Title */}
                <div className="mt-10">
                    <p className="text-sm uppercase tracking-widest text-black dark:text-primary font-medium mb-2">
                        {t('roomDetail')}
                    </p>
                    <h1 className="text-4xl md:text-5xl font-serif text-black dark:text-primary leading-tight">
                        {room.name}
                    </h1>
                </div>

                {/* Main Section */}
                <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-20 items-start mb-20">
                    {/* Carousel + Thumbnails */}
                    <div className="relative w-full">
                        {/* Main Image */}
                        <div
                            className="relative w-full aspect-[4/3] overflow-hidden rounded-2xl"
                            onClick={() => setIsLightboxOpen(true)}
                        >
                            <Lens>
                                <Image
                                    key={activeIndex}
                                    src={room.images[activeIndex]}
                                    alt={room.name}
                                    fill
                                    className="object-cover transition-all duration-500"
                                />
                            </Lens>
                        </div>

                        {/* Thumbnails Carousel */}
                        <div className="mt-8">
                            <Carousel setApi={setCarouselApi}>
                                <CarouselContent>
                                    {room.images.map((img: string, index: number) => (
                                        <CarouselItem
                                            key={index}
                                            className="w-auto max-w-[110px] h-[90px] shrink-0 px-1 cursor-pointer"
                                            onClick={() => {
                                                setActiveIndex(index);
                                                carouselApi?.scrollTo(index);
                                            }}
                                        >
                                            <motion.div
                                                whileHover={{ scale: 1.07, rotate: '-1.5deg' }}
                                                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                                                className={`relative w-full h-full rounded-md border ${activeIndex === index ? 'border-[#C2925B]' : 'border-transparent'
                                                    }`}
                                            >
                                                <Image
                                                    src={img}
                                                    alt={`thumbnail ${index + 1}`}
                                                    fill
                                                    className="object-cover rounded-md"
                                                />
                                            </motion.div>
                                        </CarouselItem>
                                    ))}
                                </CarouselContent>
                            </Carousel>
                        </div>
                    </div>

                    {/* Right Description */}
                    <motion.div
                        initial={{ opacity: 0, y: 40 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, ease: 'easeOut' }}
                        className="space-y-6"
                    >
                        <AnimatedText
                            text={room.description}
                            className="text-muted-foreground text-lg leading-relaxed"
                        />

                        <div className="grid grid-cols-2 gap-4">
                            {room.amenities.map((amenity: string) => (
                                <div key={amenity} className="flex items-center gap-2">
                                    {amenityIcons[amenity] ?? <span className="w-6" />}
                                    <span className="text-sm text-muted-foreground">{amenity}</span>
                                </div>
                            ))}
                        </div>

                        <div className="pt-4">
                            <Link href='https://www.mews.li/distributor/b33f587b-8ffc-417a-8af2-ab5700ddd7c3' target='_blank'>
                                <Button variant="default" className='cursor-pointer'>{t('bookThisRoom')}</Button>
                            </Link>
                        </div>
                    </motion.div>
                </div>

                {/* Lightbox */}
                <Lightbox
                    open={isLightboxOpen}
                    close={() => setIsLightboxOpen(false)}
                    index={activeIndex}
                    slides={room.images.map((src) => ({ src }))}
                    plugins={[Zoom]}
                />
            </section>
            <RoomCarousel title={t('otherSuitesRooms')}></RoomCarousel>
        </>
    );
}
