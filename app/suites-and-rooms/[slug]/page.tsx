'use client';

import { use } from 'react';
import { rooms, Room } from '@/app/suites-and-rooms/lib/rooms';
import { notFound } from 'next/navigation';
import RoomDetailClient from './room-detail-client';

export default function RoomDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = use(params);
  const room: Room | undefined = rooms.find((r) => r.slug === slug);

  if (!room) return notFound();

  return <RoomDetailClient room={room} />;
}
