'use client';

import { Carousel } from './ui/carousel';
import { useTranslations } from 'next-intl';

export function ExperiencesSection() {
  const t = useTranslations('experiences-section');
  const slideData = [
    {
      title: t('romanceExperience'),
      button: t('explore'),
      src: '/images/experiences/romance.webp',
    },
    {
      title: t('discoveryExperience'),
      button: t('explore'),
      src: '/images/experiences/discovery.webp',
    },
    {
      title: t('gastronomyExperience'),
      button: t('explore'),
      src: '/images/experiences/gastronomy.webp',
    },
    {
      title: t('golf'),
      button: t('explore'),
      src: '/images/experiences/golf.webp',
    },
  ];
  return (
    <section className='py-10 px-4 md:px-8 lg:px-16 w-full'>
      <div className='mx-auto max-w-6xl text-center w-fit'>
        <h2 className='text-4xl font-serif text-black dark:text-white'>{t('title')}</h2>
      </div>

      <div className='relative overflow-hidden w-full h-full py-20'>
        <Carousel slides={slideData} />
      </div>
    </section>
  );
}
