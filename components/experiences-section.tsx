'use client';

import { Carousel } from './ui/carousel';

export function ExperiencesSection() {
  const slideData = [
    {
      title: 'Romance Experience',
      button: 'Explore',
      src: '/images/experiences/romance.webp',
    },
    {
      title: 'Discovery Experience',
      button: 'Explore',
      src: '/images/experiences/discovery.webp',
    },
    {
      title: 'Gastronomy Experience',
      button: 'Explore',
      src: '/images/experiences/gastronomy.webp',
    },
    {
      title: 'Golf',
      button: 'Explore',
      src: '/images/experiences/golf.webp',
    },
  ];
  return (
    <section className='py-10 px-4 md:px-8 lg:px-16 w-full'>
      <div className='mx-auto max-w-6xl text-center w-fit'>
        <h2 className='text-4xl font-serif text-black dark:text-white'>Our Experiences</h2>
      </div>

      <div className='relative overflow-hidden w-full h-full py-20'>
        <Carousel slides={slideData} />
      </div>
    </section>
  );
}
