'use client';

import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
// import { ColourfulText } from "./ui/colourful-text";

export default function VideoSection() {
  const t = useTranslations('video-section');
  return (
    <section className='w-full bg-background text-[#FFFFE3] mx-auto'>
      <div
        className={cn(
          'w-full bg-fixed bg-center bg-cover relative flex items-center justify-center py-[60px]'
        )}
        style={{ backgroundImage: `url(/images/sliders/2.webp)` }}
      >
        {/* Overlay */}
        <div className='absolute inset-0 bg-black/40 dark:bg-black/60 z-10' />

        {/* Content */}
        <div className='relative z-20 max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 items-center px-5'>
          {/* Text Section */}
          <div>
            <h2 className='text-2xl md:text-4xl font-semibold mb-4 text-center'>
              {t('title')}
            </h2>
          </div>

          {/* Video Section */}
          <div className='aspect-w-16 aspect-h-20'>
            <iframe className='w-full h-80' src="https://www.youtube-nocookie.com/embed/-l1ZkDSThX8?si=jH_603BTNPZ5pVAC" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerPolicy="strict-origin-when-cross-origin" allowFullScreen></iframe>
          </div>
        </div>
      </div>
    </section>
  );
}
