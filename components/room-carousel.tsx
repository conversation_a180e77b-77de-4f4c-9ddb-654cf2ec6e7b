'use client';

import { Gallery4 } from '@/components/blocks/gallery4';
import { Bed, Bath, Users, Ruler } from 'lucide-react';
import { rooms } from '@/app/suites-and-rooms/lib/rooms';
import Link from 'next/link';
import { Button } from './ui/button';
import { useTranslations } from 'next-intl';

export function RoomCarousel({ title, subtitle }: { title?: string; subtitle?: string }) {
  const t = useTranslations('room-carousel');
  const roomCards = rooms.map((room) => ({
    id: room.id,
    title: room.name,
    href: `/suites-and-rooms/${room.slug}`,
    image: room.images[0],
    description: (
      <div className="inset-0 text-white">
        {/* Top-left badge */}
        <div className="absolute top-4 left-4">
          <span className="text-sm text-2xl font-semibold mb-4 tracking-wide uppercase bg-[#a0855b] px-2 py-1 rounded text-white">
            {room.pricePerNight?.replace('From ', '')} / {t('night')}
          </span>
        </div>

        {/* Centered content */}
        <div className="flex flex-col items-center justify-center text-center h-full px-4 space-y-4">
          <div className="flex flex-wrap justify-center gap-x-4 gap-y-2 text-sm text-white max-w-[90%] mx-auto">
            <span className="flex items-center gap-1 text-white">
              <Ruler className="w-4 h-4" /> {room.size}
            </span>
            <span className="flex items-center gap-1 text-white">
              <Users className="w-4 h-4" /> {room.maxGuests} Guests
            </span>
            <span className="flex items-center gap-1 text-white">
              <Bed className="w-4 h-4" /> {room.bed}
            </span>
            <span className="flex items-center gap-1 text-white">
              <Bath className="w-4 h-4" /> 1 Bathroom
            </span>
          </div>
        </div>
      </div>
    ),
  }));

  return (
    <section className="py-20 px-4 md:px-8 lg:px-16 w-full">
      <div className="mx-auto w-full px-4 md:px-8 lg:px-16 max-w-screen-2xl">
        <div className='mx-auto max-w-6xl text-center w-fit'>
          <h2 className='text-4xl md:text-5xl font-serif text-black dark:text-primary'>{title || t('title')}</h2>
          <svg id="a" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 208.33 7.34" className="w-80 text-gray-800 dark:text-[#FFFFE3] mx-auto my-3 block align-middle leading-none" fill="currentColor"><path d="M104.43,2.94c-1.86-1.1-3.91-1.79-6.01-2.13-2.11-.33-4.26-.25-6.38-.05-4.25.47-8.39,1.61-12.47,2.92l-6.13,2c-2.04.63-4.13,1.34-6.35,1.57-4.38.48-9.05-.73-12.37-3.92l.41.18-13.65-.04c-4.55-.03-9.1-.01-13.65-.07l-13.65-.16c-4.55-.1-9.63-.15-14.18-.28,4.55-.13,9.63-.19,14.18-.28l13.65-.16c4.55-.06,9.1-.05,13.65-.07l13.65-.04c.15,0,.29.06.39.16l.03.03c1.4,1.39,3.3,2.48,5.27,3.07,1.98.62,4.09.81,6.17.6,2.08-.2,4.1-.86,6.16-1.48,2.05-.64,4.11-1.28,6.17-1.88,4.13-1.21,8.35-2.29,12.67-2.64,2.16-.12,4.34-.11,6.48.3,2.12.42,4.17,1.22,5.96,2.42Z" /><path d="M117.55,2.84c1.79-1.18,3.85-1.96,5.97-2.33,2.12-.39,4.3-.43,6.45-.29,4.31.31,8.52,1.38,12.65,2.53l6.17,1.81c2.06.61,4.09,1.23,6.16,1.38,4.13.44,8.41-.54,11.88-3.09-1.39,1.68-3.36,2.81-5.44,3.53-2.1.66-4.36.89-6.55.65-2.2-.24-4.29-.95-6.33-1.55l-6.13-1.93c-4.08-1.25-8.22-2.37-12.46-2.8-4.2-.45-8.66-.11-12.38,2.1Z" /><path d="M104.43,2.94c1.79-1.2,3.84-2,5.96-2.42,2.13-.42,4.32-.42,6.48-.3,4.33.35,8.54,1.43,12.67,2.64l6.17,1.88c2.06.62,4.08,1.28,6.16,1.48,2.08.21,4.19.02,6.17-.59,1.97-.59,3.87-1.68,5.27-3.07l.02-.02c.11-.11.25-.16.39-.16l13.97.04,13.97.07,12.68.16c4.66.09,9.31.15,13.97.28-4.66.13-9.32.19-13.97.28l-12.68.16-13.97.07-13.97.04.41-.18c-3.31,3.19-7.98,4.4-12.37,3.92-2.21-.23-4.31-.94-6.35-1.57l-6.13-2c-4.08-1.31-8.22-2.45-12.47-2.92-2.12-.2-4.27-.27-6.38.06-2.1.35-4.15,1.03-6.01,2.14Z" /><path d="M43.87,2.84c1.64,1.38,3.6,2.33,5.64,2.82,2.04.54,4.17.65,6.24.39,2.08-.26,4.09-.9,6.14-1.53l6.14-1.91c4.13-1.17,8.34-2.25,12.68-2.56,2.16-.1,4.35-.09,6.49.33,2.12.46,4.18,1.24,5.95,2.46-1.9-1-3.95-1.64-6.04-1.91-2.1-.3-4.23-.21-6.33-.01-2.1.26-4.19.61-6.27,1.09-2.07.5-4.12,1.05-6.17,1.68l-6.16,1.81c-2.05.58-4.15,1.27-6.35,1.41-2.18.17-4.4-.05-6.48-.72-1.04-.33-2.03-.79-2.96-1.35-.93-.55-1.81-1.2-2.53-2Z" /><path d="M7,1.78c-1.43,1.05-4.47,1.16-4.47,1.16,0,0,3.05.11,4.47,1.16,1.43,1.05,2.32-.24,1.67-1.16.65-.92-.24-2.22-1.67-1.16Z" /><path d="M2.94,2.94c0,.39-.32.71-.71.71s-.71-.32-.71-.71.32-.71.71-.71.71.32.71.71Z" /><path d="M201.32,4.11c1.43-1.05,4.48-1.16,4.48-1.16,0,0-3.05-.11-4.48-1.16-1.43-1.05-2.32.24-1.66,1.16-.66.92.23,2.22,1.66,1.16Z" /><path d="M205.39,2.94c0-.39.32-.71.71-.71s.71.32.71.71-.32.71-.71.71-.71-.32-.71-.71Z" /></svg>
          <p className="text-md tracking-widest text-black dark:text-primary font-medium text-center mt-2 mb-12 leading-snug mt-2 mb-12 leading-snug">{subtitle || t('subtitle')}</p>
        </div>
        <Gallery4 items={roomCards} />
        <div className='w-fit mx-auto'>
          <Link href='/suites-and-rooms'>
            <Button>{t('discoverAll')}</Button>
          </Link>
        </div>
      </div>

    </section>
  );
}
