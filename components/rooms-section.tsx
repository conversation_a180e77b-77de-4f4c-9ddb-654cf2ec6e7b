'use client';

import { useState } from 'react';
import { Lens } from './ui/lens';
import { motion } from 'motion/react';
import Image from 'next/image';
import { GlowingEffect } from './ui/glowing-effect';
import { Carousel } from './ui/apple-cards-carousel';
import { Button } from './ui/button';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export function RoomsSection() {
  const t = useTranslations('rooms-section');
  const cards = [
    <CarouselItem
      key={1}
      title='Honeymoon Suite Guépard'
      description="The Guépard Suite aptly named 'The Honeymoon Suite', is the second largest suite (117m2)..."
      href='/suites-and-rooms/honeymoon-suite-guepard'
      src='/images/rooms/guepard.webp'
    />,
    <CarouselItem
      key={2}
      title='Grand Master Suite Panthère'
      description='The elegant Panthère Suite is one of the largest suites in the riad (68m2) and boasts a large living area...'
      href='/suites-and-rooms/grand-master-suite-panthere'
      src='/images/rooms/panthere.webp'
    />,
    <CarouselItem
      key={3}
      title='Master Suite Zèbre'
      description='The Master Suite Zèbre, beautifully decorated in rich black and white fabrics, antiques and unique art...'
      href='/suites-and-rooms/master-suite-zebre'
      src='/images/rooms/zebre.webp'
    />,
    <CarouselItem
      key={4}
      title='Master Suite Chameau'
      description='The delightful Master Suite Chameau (76m2) is situated in the Winter Courtyard and boasts the second largest...'
      href='/suites-and-rooms/master-suite-chameau'
      src='/images/rooms/chameau.webp'
    />,
    <CarouselItem
      key={5}
      title='Suite Eléphant'
      description='The Eléphant Suite (54m2) is adorned with African art and antiques and features a living area (44m2) with fireplace...'
      href='/suites-and-rooms/suite-elephant'
      src='/images/rooms/elephant.webp'
    />,
    <CarouselItem
      key={6}
      title='Junior Suite Renard des Sables'
      description='Junior Suite Renard Des Sables (32m2), situated in the Winter Courtyard and decorated in rich red fabrics and ...'
      href='/suites-and-rooms/junior-suite-renard-des-sables'
      src='/images/rooms/renard-des-sables.webp'
    />,
    <CarouselItem
      key={7}
      title='Junior Suite Caméléon'
      description='Junior Suite Caméléon (32m2) decorated in hues of blue, antiques and artisanal wood, overlooks the Winter Courtyard...'
      href='/suites-and-rooms/junior-suite-cameleon'
      src='/images/rooms/cameleon.webp'
    />,
    <CarouselItem
      key={8}
      title='Room Tourterelles'
      description='Room Tourterelles (23m2) situated in the Winter Courtyard is light and romantic with floaty fabrics and ornate wood...'
      href='/suites-and-rooms/master-suite-chameau'
      src='/images/rooms/tourterelles.webp'
    />,
    <CarouselItem
      key={9}
      title='Room Gazelle'
      description='Room Gazelle (22m2) with rich fawn fabrics and antique wood furniture is situated in the Winter Courtyard and features...'
      href='/suites-and-rooms/room-guazelle'
      src='/images/rooms/guazelle.webp'
    />,
  ];
  return (
    <section className='py-10 px-4 md:px-8 lg:px-16 w-full'>
      <div className='mx-auto max-w-6xl text-center w-fit'>
        <h2 className='text-4xl font-serif text-black dark:text-white'>{t('title')}</h2>
      </div>
      <Carousel items={cards} />
      <div className='w-fit mx-auto'>
        <Link href='/suites-and-rooms' target='_blank'>
          <Button>{t('discoverAll')}</Button>
        </Link>
      </div>
    </section>
  );
}

interface CarouselItemProps {
  src: string;
  title: string;
  href: string;
  description: React.ReactNode;
}
const CarouselItem = ({ src, title, description, href }: CarouselItemProps) => {
  const [hovering, setHovering] = useState(false);

  return (
    <div className='relative h-full min-h-[20rem] w-full min-w-[calc(100vw-30vw)] md:min-w-[calc(100vw-50vw)] dark:border dark:border-primary/20 lg:min-w-[calc(100vw-70vw)] xl:min-w-[calc(100vw-80vw)] rounded-2xl p-2 md:rounded-3xl md:p-3'>
      <GlowingEffect spread={40} glow={true} disabled={false} proximity={64} inactiveZone={0.01} />
      <div className='relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border dark:shadow-[0px_0px_15px_0px_#FFFFE3] shadow-[0px_0px_15px_0px_#00000050] p-6 md:p-6 dark:border-0 dark:shadow-[0px_0px_27px_0px_#2D2D2D]'>
        <div className='relative z-10 flex flex-col flex-1'>
          <Lens hovering={hovering} setHovering={setHovering}>
            <Image
              src={src}
              alt='image'
              height={300}
              width={300}
              style={{ objectFit: 'cover', objectPosition: 'center' }}
              className='rounded-2xl w-full h-60 md:h-60 lg:h-60'
              loading='eager'
            />
          </Lens>
          <div className='flex flex-col justify-between flex-1'>
            <motion.div
              animate={{
                filter: hovering ? 'blur(2px)' : 'blur(0px)',
              }}
              className='py-4 relative z-20'
            >
              <h2 className='text-black dark:text-white text-2xl text-left font-bold'>{title}</h2>
              <p className='text-neutral-800 dark:text-neutral-300 text-left  mt-4'>
                {description}
              </p>
            </motion.div>

            <motion.div
              animate={{
                filter: hovering ? 'blur(2px)' : 'blur(0px)',
              }}
              className='py-4 relative z-20'
            >
              <Link href={href} target='_blank'>
                <Button style={{ cursor: 'pointer' }}>Discover</Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};
