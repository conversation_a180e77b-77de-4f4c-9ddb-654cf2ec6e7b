import { JSX, ReactNode } from 'react';

import { cn } from '@/lib/utils';

import { <PERSON>er, FooterBottom, FooterColumn, FooterContent } from '@/components/ui/footer';
import { ModeToggle } from '@/components/ui/mode-toggle';
import { LangToggle } from '@/components/ui/lang-toggle';
import Image from 'next/image';
import ParallaxBanner from './ui/parallax-banner';
import Link from 'next/link';
import {
  IconMailFilled,
  IconMapPinFilled,
  IconPhoneFilled,
  IconPrinter,
} from '@tabler/icons-react';
import SocialIconButton from './social-media';

interface FooterLink {
  text: string;
  icon?: JSX.Element | null;
  href: string;
}

interface FooterColumnProps {
  title: string;
  links: FooterLink[];
  social?: boolean;
  map?: boolean;
}

interface FooterProps {
  logo?: ReactNode;
  name?: string;
  columns?: FooterColumnProps[];
  copyright?: string;
  policies?: FooterLink[];
  showModeToggle?: boolean;
  className?: string;
}

export default function FooterSection({
  logo = (
    <Image
      src='/logo.svg'
      alt='logo'
      width={200}
      height={200}
      className='mx-auto bg-black/70 dark:bg-transparent rounded-xl p-2'
      loading='eager'
    />
  ),
  columns = [
    {
      title: 'Suites & Rooms',
      links: [
        { text: 'Honeymoon Suite Guépard', href: '/suites-and-rooms/honeymoon-suite-guepard' },
        {
          text: 'Grand Master Suite Panthère',
          href: '/suites-and-rooms/grand-master-suite-panthere',
        },
        { text: 'Master Suite Zèbre', href: '/suites-and-rooms/master-suite-zebre' },
        { text: 'Master Suite Chameau', href: '/suites-and-rooms/master-suite-chameau' },
        { text: 'Suite Eléphant', href: '/suites-and-rooms/suite-elephant' },
        {
          text: 'Junior Suite Renard des Sables',
          href: '/suites-and-rooms/junior-suite-renard-des-sables',
        },
        { text: 'Junior Suite Caméléon', href: '/suites-and-rooms/junior-suite-cameleon' },
        { text: 'Room Tourterelles', href: '/suites-and-rooms/master-suite-chameau' },
        { text: 'Room Gazelle', href: '/suites-and-rooms/room-guazelle' },
      ],
    },
    {
      title: 'Useful links',
      links: [
        { text: 'Home', href: '/' },
        { text: 'Suites & Rooms', href: '/suites-and-rooms/' },
        { text: "Charlie's Bar", href: '/marrakech-bar-cocktails' },
        { text: "Coco's Spa", href: '/marrakech-luxury-spa/' },
        { text: 'Events', href: '/events' },
        { text: 'Gastronomy', href: '/gastronomy' },
        { text: 'Experiences', href: '/experiences' },
        { text: 'Gallery', href: '/gallery' },
        { text: 'Contact', href: '/contact' },
      ],
    },
    {
      title: 'Contact',
      links: [
        { icon: <IconPhoneFilled />, text: '+212 524-380-975', href: 'tel:+212524380975' },
        { icon: <IconPrinter />, text: '+212 524-381-653', href: 'tel:+212524381653' },
        { icon: <IconMailFilled />, text: '<EMAIL>', href: 'mailto:<EMAIL>' },
        {
          icon: <IconMapPinFilled />,
          text: '31–33, Derb Jdid, Bab Doukkala, Marrakech',
          href: 'https://maps.google.com/maps?ll=31.633595,-7.995628&z=15&t=m&hl=en-US&gl=US&mapclient=embed&cid=15408652692409214544',
        },
      ],
      social: true,
      map: true,
    },
  ],
  copyright = "© 2025 Noir D'Ivoire. All rights reserved",
  policies = [
    { text: 'Privacy Policy', href: '/terms-conditions' },
    { text: 'Terms of Service', href: '/terms-conditions' },
  ],
  showModeToggle = true,
  className,
}: FooterProps) {
  return (
    <footer className={cn('bg-background w-full', className)}>
      <ParallaxBanner
        src='/images/sliders/12.webp'
        text='Book directly and'
        colored='save 10-15%'
        cta='Book now →'
        href='https://www.mews.li/distributor/b33f587b-8ffc-417a-8af2-ab5700ddd7c3'
        height='h-70'
      ></ParallaxBanner>
      <div className='max-w-container mx-auto px-4'>
        <Footer>
          <FooterContent className='mx-auto'>
            <FooterColumn className='col-span-1 sm:col-span-2 md:col-span-3 lg:col-span-1'>
              <div className='flex px-5 pb-5 items-center gap-5 flex-col'>
                {logo}
                <p className='max-w-[600px] lg:max-w-[15rem] text-neutral-600 dark:text-neutral-400 text-center'>
                  A unique oasis in the ancient Medina of Marrakech, steeped in history. With
                  stunning architecture, extraordinary character and expressive décor, we inspire
                  and excite our guests.
                </p>
              </div>
            </FooterColumn>
            {columns.map((column, index) => (
              <FooterColumn
                key={index}
                className='mx-auto w-full p-5'
              >
                <h3 className='text-md pt-1 font-semibold'>{column.title}</h3>
                {column.links.map((link, linkIndex) =>
                  link.href?.startsWith('https://') ? (
                    <a
                      key={linkIndex}
                      href={link.href}
                      target='_blank'
                      className='text-muted-foreground text-sm flex items-center gap-2'
                    >
                      {link.icon ?? <></>}
                      {link.text}
                    </a>
                  ) : (
                    <Link
                      key={linkIndex}
                      href={link.href}
                      className='text-muted-foreground text-sm flex items-center gap-2'
                    >
                      {link.icon ?? <></>}
                      {link.text}
                    </Link>
                  )
                )}
                {column.map ? (
                  <iframe
                    title="Noir D'ivoire location"
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3397.0091537972876!2d-7.997817085090785!3d31.633599748707812!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xdafee41aea941ff%3A0xd5d687fc366ae650!2sRiad+Noir+D'Ivoire!5e0!3m2!1sen!2sus!4v1535120205919"
                    className='rounded-lg'
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  ></iframe>
                ) : (
                  <></>
                )}

                {column.social ? (
                  <div className='flex gap-4 items-center justify-center mt-2'>
                    <SocialIconButton platform='facebook' href='https://www.facebook.com/officialnoirdivoire/' />
                    <SocialIconButton platform='x' href='https://x.com' />
                    <SocialIconButton platform='instagram' href='https://www.instagram.com/riadnoirdivoire/' />
                    <SocialIconButton platform='youtube' href='https://www.youtube.com/@riadnoirdivoire3040' />
                  </div>
                ) : (
                  <></>
                )}
              </FooterColumn>
            ))}
          </FooterContent>
          <FooterBottom>
            <div>{copyright}</div>
            <div className='flex items-center gap-4'>
              {policies.map((policy, index) => (
                <a key={index} href={policy.href}>
                  {policy.text}
                </a>
              ))}
              {showModeToggle && <LangToggle />}
              {showModeToggle && <ModeToggle />}
            </div>
          </FooterBottom>
        </Footer>
      </div>
    </footer>
  );
}
