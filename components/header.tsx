'use client';

import React, { useState } from 'react';
import {
  Navbar,
  NavBody,
  NavbarLogo,
  MobileNav,
  MobileNavHeader,
  MobileNavMenu,
  MobileNavToggle,
} from '@/components/ui/resizable-navbar';
import { Menu, MenuItem, ProductItem } from '@/components/ui/navbar-menu';
import { ModeToggle } from './ui/mode-toggle';
import { LangToggle } from './ui/lang-toggle';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

export const Header = () => {
  const [active, setActive] = useState<string | null>(null);
  const [mobileOpen, setMobileOpen] = useState<boolean>(false);

  const toggleMobile = () => setMobileOpen((prev) => !prev);
  const t = useTranslations('header');

  return (
    <Navbar className='top-0 z-50'>
      {/* Desktop Header */}
      <NavBody>
        <div className='flex items-center'>
          <NavbarLogo />
        </div>

        <div className='flex flex-1 justify-center'>
          <Menu setActive={setActive}>
            <MenuItem item={t('suitesRooms')} href='/suites-and-rooms' active={active} setActive={setActive}>
              <div className='grid grid-cols-2 gap-4'>
                <ProductItem
                  title={t('honeymoonSuiteGuepard.title')}
                  description={t('honeymoonSuiteGuepard.description')}
                  href='/suites-and-rooms/honeymoon-suite-guepard'
                  src='/images/rooms/guepard.webp'
                />
                <ProductItem
                  title={t('grandMasterSuitePanthere.title')}
                  description={t('grandMasterSuitePanthere.description')}
                  href='/suites-and-rooms/grand-master-suite-panthere'
                  src='/images/rooms/panthere.webp'
                />
                <ProductItem
                  title={t('masterSuiteZebre.title')}
                  description={t('masterSuiteZebre.description')}
                  href='/suites-and-rooms/master-suite-zebre'
                  src='/images/rooms/zebre.webp'
                />
                <ProductItem
                  title={t('masterSuiteChameau.title')}
                  description={t('masterSuiteChameau.description')}
                  href='/suites-and-rooms/master-suite-chameau'
                  src='/images/rooms/chameau.webp'
                />
              </div>
              <div className='mt-4 text-center'>
                <Link href='/suites-and-rooms'>
                  <Button style={{ cursor: 'pointer' }}>
                    {t('moreSuitesButton')}
                  </Button>
                </Link>
              </div>
            </MenuItem>

            <MenuItem
              item={t('charliesBar')}
              href='/marrakech-bar-cocktails'
              active={active}
              setActive={setActive}
            />

            <MenuItem item={t('cocosSpa')} href='/marrakech-luxury-spa' active={active} setActive={setActive}>
              <div className='grid grid-cols-2 gap-4'>
                <ProductItem
                  title={t('hammam.title')}
                  description={t('hammam.description')}
                  href='/marrakech-luxury-spa/hammam'
                  src='/images/spa/hammam.webp'
                />
                <ProductItem
                  title={t('massages.title')}
                  description={t('massages.description')}
                  href='/marrakech-luxury-spa/massages'
                  src='/images/spa/massage.webp'
                />
                <ProductItem
                  title={t('spaPackages.title')}
                  description={t('spaPackages.description')}
                  href='/marrakech-luxury-spa/spa-packages'
                  src='/images/spa/spa-packages.webp'
                />
                <ProductItem
                  title={t('otherTreatments.title')}
                  description={t('otherTreatments.description')}
                  href='/marrakech-luxury-spa/other-treatments'
                  src='/images/spa/others.webp'
                />
              </div>
              <div className='mt-4 text-center'>
                <Link href='/marrakech-luxury-spa'>
                  <Button style={{ cursor: 'pointer' }}>
                    {t('discoverAllButton')}
                  </Button>
                </Link>
              </div>
            </MenuItem>

            <MenuItem item={t('more')} active={active} setActive={setActive}>
              <div className='flex flex-col gap-3'>
                <Link href='/events'>{t('events')}</Link>
                <Link href='/gastronomy'>{t('gastronomy')}</Link>
                <Link href='/experiences'>{t('experiences')}</Link>
                <Link href='/gallery'>{t('gallery')}</Link>
                <Link href='/contact'>{t('contact')}</Link>
              </div>
            </MenuItem>
          </Menu>
        </div>

        <div className='flex justify-end space-x-2 items-center gap-1'>
          <LangToggle />
          <ModeToggle />
        </div>
      </NavBody>

      {/* Mobile Header */}
      <MobileNav>
        <MobileNavHeader>
          <NavbarLogo />
          <MobileNavToggle isOpen={mobileOpen} onClick={toggleMobile} />
        </MobileNavHeader>

        <MobileNavMenu isOpen={mobileOpen} onClose={() => setMobileOpen(false)}>
          <Link href='/suites-and-rooms' onClick={toggleMobile}>
            {t('suitesRooms')}
          </Link>
          <Link href='/marrakech-bar-cocktails' onClick={toggleMobile}>
            {t('charliesBar')}
          </Link>
          <Link href='/marrakech-luxury-spa' onClick={toggleMobile}>
            {t('cocosSpa')}
          </Link>
          <Link href='/events' onClick={toggleMobile}>
            {t('events')}
          </Link>
          <Link href='/gastronomy' onClick={toggleMobile}>
            {t('gastronomy')}
          </Link>
          <Link href='/experiences' onClick={toggleMobile}>
            {t('experiences')}
          </Link>
          <Link href='/gallery' onClick={toggleMobile}>
            {t('gallery')}
          </Link>
          <Link href='/contact' onClick={toggleMobile}>
            {t('contact')}
          </Link>
          <div className='pt-4 w-full flex items-center justify-center gap-3'>
            <a
              href='https://www.mews.li/distributor/b33f587b-8ffc-417a-8af2-ab5700ddd7c3'
              target='_blank'
            >
              <Button>{t('bookNow')}</Button>
            </a>
            <LangToggle />
            <ModeToggle />
          </div>
        </MobileNavMenu>
      </MobileNav>
    </Navbar>
  );
};
