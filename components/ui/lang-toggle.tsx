'use client';

import * as React from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Image from 'next/image';
import { Globe } from 'lucide-react';

export function LangToggle() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [locale, setLocale] = React.useState("")

  React.useEffect(() => {
    const cookieLocale = document.cookie
      .split('; ')
      .find((row) => row.startsWith('NEXT_LOCALE='))
      ?.split('=')[1];
    if (cookieLocale) {
      setLocale(cookieLocale);
    } else {
      const browserLocale = navigator.language.slice(0, 2);
      setLocale(browserLocale);
      document.cookie = `NEXT_LOCALE=${browserLocale}; path=/; max-age=31536000`;
      router.refresh();
    }
  }, [router]);
  
  const switchLanguage = (lang: string) => {
    setLocale(lang);
    document.cookie = `NEXT_LOCALE=${lang}; path=/; max-age=31536000`;
    router.refresh();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" style={{ cursor: 'pointer' }}>
          <Globe className="h-[1.2rem] w-[1.2rem]" />
          <span className="sr-only">Switch language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => switchLanguage('en')}
          className={`flex items-center cursor-pointer gap-2 ${locale === 'en' ? 'bg-muted' : ''}`}
        >
          <Image src="/images/flags/united-kingdom--3752.svg" className='rounded-full' alt="English" width={20} height={15} />
          English
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => switchLanguage('fr')}
          className={`flex items-center cursor-pointer gap-2 ${locale === 'fr' ? 'bg-muted' : ''}`}
        >
          <Image src="/images/flags/france--3686.svg" className='rounded-full' alt="French" width={20} height={20} />
          Français
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
