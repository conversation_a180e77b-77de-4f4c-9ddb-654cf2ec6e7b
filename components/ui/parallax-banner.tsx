// components/ParallaxBanner.tsx
'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { ColourfulText } from './colourful-text';
import { Button } from './button';
import { useTranslations } from 'next-intl';

interface ParallaxBannerProps {
  src: string;
  text?: string;
  colored?: string;
  cta?: string;
  href: string;
  height: string;
}

const ParallaxBanner: React.FC<ParallaxBannerProps> = ({
  src,
  text,
  cta,
  height = 'h-60',
  href,
  colored,
}) => {
  const t = useTranslations('parallax-banner');
  return (
    <div
      className={cn(
        'w-full bg-fixed bg-center bg-cover relative flex items-center justify-center',
        height
      )}
      style={{ backgroundImage: `url(${src})` }}
    >
      {/* Overlay */}
      <div className='absolute inset-0 bg-black/40 dark:bg-black/60 z-10' />

      {/* Content */}
      <div className='relative z-20 text-center px-4 text-white'>
        <h2 className='text-2xl md:text-4xl font-semibold mb-4'>
          {text || t('bookDirectly')} <ColourfulText text={colored || t('save')} />
        </h2>
        <a href={href} target='_blank'>
          <Button>{cta || t('bookNow')}</Button>
        </a>
      </div>
    </div>
  );
};

export default ParallaxBanner;
