'use client';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence, Variants } from 'motion/react';
import React, { useEffect, useState } from 'react';

export const ImagesSlider = ({
  images,
  children,
  overlay = true,
  overlayClassName,
  className,
  autoplay = true,
  direction = 'up',
  transitionStyle = 'fade'
}: {
  images: string[];
  children: React.ReactNode;
  overlay?: React.ReactNode;
  overlayClassName?: string;
  className?: string;
  autoplay?: boolean;
  direction?: 'up' | 'down';
  transitionStyle?: 'scale-rotate' | 'fade' | 'slide' | 'flip' | 'zoom' | 'blurFade' | 'kenBurns' | 'dreamyZoom'; // <-- SUPPORTED STYLES
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loadedImages, setLoadedImages] = useState<string[]>([]);

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1 === images.length ? 0 : prevIndex + 1));
  };

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 < 0 ? images.length - 1 : prevIndex - 1));
  };

  const loadImages = () => {
    // setLoading(true);
    const loadPromises = [...images, '/logo.svg'].map((image) => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.src = image;
        img.onload = () => resolve(image);
        img.onerror = reject;
      });
    });

    Promise.all(loadPromises)
      .then((loadedImages) => {
        setLoadedImages(loadedImages as string[]);
        // setLoading(false);
      })
      .catch((error) => console.error('Failed to load images', error));
  };

  useEffect(() => {
    loadImages();
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowRight') {
        handleNext();
      } else if (event.key === 'ArrowLeft') {
        handlePrevious();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    let interval: NodeJS.Timeout;
    if (autoplay) {
      interval = setInterval(() => {
        handleNext();
      }, 5000);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      clearInterval(interval);
    };
  }, [autoplay]);

  // Animation Variants
  // Update the animationVariants definition
  const animationVariants: Record<string, Variants> = {
    'scale-rotate': {
      initial: { scale: 0, opacity: 0, rotateX: 45 },
      animate: {
        scale: 1,
        opacity: 1,
        rotateX: 0,
        transition: { duration: 0.5 },
      },
      exit: {
        opacity: 1,
        y: direction === 'up' ? '-150%' : '150%',
        transition: { duration: 0.8 },
      },
    },
    fade: {
      initial: { opacity: 0 },
      animate: { opacity: 1, transition: { duration: 0.8 } },
      exit: { opacity: 0, transition: { duration: 0.8 } },
    },
    slide: {
      initial: {
        x: direction === 'up' ? '100%' : '-100%',
        opacity: 0,
      },
      animate: {
        x: 0,
        opacity: 1,
        transition: { duration: 0.8 },
      },
      exit: {
        x: direction === 'up' ? '-100%' : '100%',
        opacity: 0,
        transition: { duration: 0.8 },
      },
    },
    zoom: {
      initial: { scale: 1.2, opacity: 0 },
      animate: {
        scale: 1,
        opacity: 1,
        transition: { duration: 1 },
      },
      exit: {
        scale: 0.9,
        opacity: 0,
        transition: { duration: 0.8 },
      },
    },
    flip: {
      initial: { rotateY: 90, opacity: 0 },
      animate: {
        rotateY: 0,
        opacity: 1,
        transition: { duration: 0.7 },
      },
      exit: {
        rotateY: -90,
        opacity: 0,
        transition: { duration: 0.7 },
      },
    },
    blurFade: {
      initial: { opacity: 0, filter: 'blur(20px)' },
      animate: {
        opacity: 1,
        filter: 'blur(0px)',
        transition: {
          opacity: { duration: 0.1, ease: "easeOut" as const },
          filter: { duration: 0.5, ease: "easeOut" as const },
          scale: { duration: 5, ease: [0.42, 0, 0.58, 1] },
        },
        scale: 1.3,
      },
      exit: {
        opacity: 0,
        filter: 'blur(20px)',
        transition: { duration: 0.1 },
      },
    },
    kenBurns: {
      initial: { scale: 1.2, opacity: 0 },
      animate: {
        scale: 1,
        opacity: 1,
        transition: { duration: 2, ease: "easeOut" as const },
      },
      exit: {
        scale: 1.1,
        opacity: 0,
        transition: { duration: 1 },
      },
    },
    dreamyZoom: {
      initial: {
        scale: 1.2,
        opacity: 0,
        filter: 'blur(15px)'
      },
      animate: {
        scale: 1.4,
        opacity: 1,
        filter: 'blur(0px)',
        transition: {
          duration: 5,
          ease: "easeInOut" as const
        },
      },
      exit: {
        scale: 1.6,
        opacity: 0,
        filter: 'blur(15px)',
        transition: { duration: 1.2, ease: "easeInOut" as const },
      },
    },
  };

  const variants = animationVariants[transitionStyle];
  const areImagesLoaded = loadedImages.length > 0;

  return (
    <div
      className={cn(
        'overflow-hidden h-full w-full relative flex items-center justify-center',
        className
      )}
      style={{ perspective: '1000px' }}
    >
      {areImagesLoaded && children}
      {areImagesLoaded && overlay && (
        <div className={cn('absolute inset-0 bg-black/50 z-40', overlayClassName)} />
      )}

      {areImagesLoaded && (
        <AnimatePresence mode='wait'>
          <motion.img
            key={currentIndex}
            src={loadedImages[currentIndex]}
            initial='initial'
            animate='animate'
            exit='exit'
            variants={variants}
            className='image h-full w-full absolute inset-0 object-cover object-center'
            alt='slide'
          />
        </AnimatePresence>
      )}
    </div>
  );
};
