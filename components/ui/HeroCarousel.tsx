// components/ui/ZoomHeroCarousel.tsx
'use client';

import { useEffect, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface ZoomHeroCarouselProps {
  images: string[];
  room: {
    name: string;
    size: string;
    bed: string;
    maxGuests: number;
    amenities: string[];
  };
}

export default function ZoomHeroCarousel({ images, room }: ZoomHeroCarouselProps) {
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrent((prev) => (prev + 1) % images.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [images]);

  return (
    <div className="relative w-full h-[80vh] overflow-hidden">
      {images.map((img, index) => (
        <div
          key={img}
          className={`absolute inset-0 bg-cover bg-center transition-all duration-1000 ease-in-out ${index === current ? 'opacity-100 scale-105 z-20' : 'opacity-0 scale-100 z-10'
            }`}
          style={{ backgroundImage: `url(${img})` }}
        />
      ))}

      {/* Overlay Text */}
      <div className="absolute inset-0 flex flex-col justify-center items-center text-white text-center z-30 bg-black/40">
        <h1 className="text-4xl font-bold uppercase tracking-wide">{room.name}</h1>
        <p className="text-lg mt-2">{room.size} • {room.bed} • Sleeps {room.maxGuests}</p>
        <div className="flex gap-4 mt-4">
          {room.amenities.slice(0, 4).map((amenity, i) => (
            <span key={i} className="text-sm bg-white/20 px-3 py-1 rounded-full">{amenity}</span>
          ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={() => setCurrent((prev) => (prev - 1 + images.length) % images.length)}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-40 bg-white/40 p-2 rounded-full"
      >
        <ChevronLeft />
      </button>
      <button
        onClick={() => setCurrent((prev) => (prev + 1) % images.length)}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-40 bg-white/40 p-2 rounded-full"
      >
        <ChevronRight />
      </button>
    </div>
  );
}
