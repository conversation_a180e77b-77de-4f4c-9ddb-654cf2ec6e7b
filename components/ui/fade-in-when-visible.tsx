'use client';

import { motion, useAnimation, useInView } from 'framer-motion';
import { useEffect, useRef } from 'react';

export function FadeInWhenVisible({
  children,
  delay = 0,
}: {
  children: React.ReactNode;
  delay: number;
}) {
  const ref = useRef(null);
  const inView = useInView(ref, { once: false, margin: '-10% 0px' });
  const controls = useAnimation();

  useEffect(() => {
    if (inView) {
      controls.start({ opacity: 1, y: 0, transition: { duration: 0.6, delay } });
    } else {
      controls.start({ opacity: 0, y: 50 });
    }
  }, [inView, controls, delay]);

  return (
    <motion.div ref={ref} className='w-full' initial={{ opacity: 0, y: 50 }} animate={controls}>
      {children}
    </motion.div>
  );
}
