'use client'

import React, { useEffect, useState, useCallback, useRef, JSX } from 'react'
import useEmblaCarousel, { UseEmblaCarouselType } from 'embla-carousel-react'
import { IconArrowNarrowLeft, IconArrowNarrowRight } from '@tabler/icons-react'
import { motion } from 'motion/react'
import { useInView } from 'framer-motion'

interface CarouselProps {
  items: JSX.Element[]
}

type EmblaApi = UseEmblaCarouselType[1]

const CarouselCard = ({ children, index }: { children: JSX.Element; index: number }) => {
  const cardRef = useRef<HTMLDivElement | null>(null)
  const cardInView = useInView(cardRef, { once: true, margin: '0px 20%' })

  return (
    <motion.div
      ref={cardRef}
      className="flex-[0_0_80%] md:flex-[0_0_60%] lg:flex-[0_0_50%] xl:flex-[0_0_40%] shrink-0"
      initial={{ opacity: 0, y: 20 }}
      animate={
        cardInView
          ? {
            opacity: 1,
            y: 0,
            transition: {
              duration: 0.5,
              delay: 0.1 * index,
              ease: 'easeOut',
            },
          }
          : {}
      }
      transition={{ duration: 0.5, delay: index * 0.05 }}

    >
      {children}
    </motion.div>
  )
}

export const Carousel = ({ items }: CarouselProps) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    dragFree: true,
    align: 'start',
  })

  const [canScrollPrev, setCanScrollPrev] = useState(false)
  const [canScrollNext, setCanScrollNext] = useState(false)

  const updateButtons = useCallback((api: EmblaApi) => {
    if (!api) return
    setCanScrollPrev(api.canScrollPrev())
    setCanScrollNext(api.canScrollNext())
  }, [])

  useEffect(() => {
    if (!emblaApi) return
    updateButtons(emblaApi)
    emblaApi.on('select', updateButtons)
    emblaApi.on('reInit', updateButtons)
  }, [emblaApi, updateButtons])

  return (
    <div className="relative w-full">
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex gap-5 pl-4 py-2">
          {items.map((item, index) => (
            <CarouselCard key={index} index={index}>
              {item}
            </CarouselCard>
          ))}
        </div>
      </div>

      {/* Controls */}
      <div className="flex justify-end gap-2 mt-6">
        <button
          onClick={() => emblaApi?.scrollPrev()}
          disabled={!canScrollPrev}
          className="group/button w-10 h-10 flex items-center justify-center bg-primary dark:bg-neutral-800 border-3 border-transparent rounded-full hover:-translate-y-0.5 active:translate-y-0.5 transition duration-200 disabled:opacity-40 cursor-pointer"
        >
          <IconArrowNarrowLeft className="h-6 w-6 text-secondary transition-transform duration-300 group-hover/button:rotate-12 dark:text-neutral-400" />
        </button>
        <button
          onClick={() => emblaApi?.scrollNext()}
          disabled={!canScrollNext}
          className="group/button w-10 h-10 flex items-center justify-center bg-primary dark:bg-neutral-800 border-3 border-transparent rounded-full hover:-translate-y-0.5 active:translate-y-0.5 transition duration-200 disabled:opacity-40 cursor-pointer"
        >
          <IconArrowNarrowRight className="h-6 w-6 text-secondary transition-transform duration-300 group-hover/button:rotate-12 dark:text-neutral-400" />
        </button>
      </div>
    </div>
  )
}
