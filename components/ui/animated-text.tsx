import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

export function AnimatedText({ text = '', className = '', initialPause = 0 }) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.2 });

  return (
    <span ref={ref} className={className} style={{ display: 'inline-block', overflow: 'hidden' }}>
      {text.split(' ').map((word, i) => (
        <motion.span
          key={i}
          initial={{ filter: 'blur(10px)', opacity: 0, y: 5 }}
          animate={isInView ? { filter: 'blur(0px)', opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.2, ease: 'easeInOut', delay: initialPause + 0.02 * i }}
          style={{ display: 'inline-block' }}
        >
          {word}&nbsp;
        </motion.span>
      ))}
    </span>
  );
}
