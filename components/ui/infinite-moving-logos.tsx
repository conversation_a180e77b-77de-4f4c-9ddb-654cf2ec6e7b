'use client';

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export const InfiniteMovingLogos = ({
  logos,
  direction = 'left',
  speed = 'normal',
  pauseOnHover = true,
  className,
  logoClassName = '',
}: {
  logos: { src: string; alt?: string }[];
  direction?: 'left' | 'right';
  speed?: 'fast' | 'normal' | 'slow';
  pauseOnHover?: boolean;
  className?: string;
  logoClassName?: string;
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const scrollerRef = React.useRef<HTMLUListElement>(null);

  useEffect(() => {
    // Duplicate logos for infinite effect
    if (containerRef.current && scrollerRef.current) {
      const items = Array.from(scrollerRef.current.children);
      items.forEach((item) => {
        const duplicate = item.cloneNode(true);
        scrollerRef.current?.appendChild(duplicate);
      });
      setStart(true);
    }
    // Set CSS vars
    if (containerRef.current) {
      containerRef.current.style.setProperty(
        '--animation-direction',
        direction === 'left' ? 'forwards' : 'reverse'
      );
      containerRef.current.style.setProperty(
        '--animation-duration',
        speed === 'fast' ? '20s' : speed === 'normal' ? '40s' : '80s'
      );
    }
    // eslint-disable-next-line
  }, []);
  const [start, setStart] = useState(false);

  return (
    <div
      ref={containerRef}
      className={cn(
        'scroller relative z-20 max-w-7xl overflow-hidden',
        '[mask-image:linear-gradient(to_right,transparent,white_16%,white_84%,transparent)]',
        className
      )}
    >
      <ul
        ref={scrollerRef}
        className={cn(
          'flex w-max min-w-full shrink-0 flex-nowrap gap-6 py-6',
          start && 'animate-scroll-logos',
          pauseOnHover && 'hover:[animation-play-state:paused]'
        )}
      >
        {logos.map((logo, idx) => (
          <li
            key={idx}
            className='flex items-center justify-center bg-black/30 dark:bg-white/80 rounded-2xl shadow-md border border-zinc-200 dark:border-zinc-700 w-46 h-30 p-2'
          >
            <Image
              src={logo.src}
              width={512}
              height={512}
              alt={logo.alt || 'Partner logo'}
              className={cn('object-contain w-full h-full', logoClassName)}
              draggable={false}
            />
          </li>
        ))}
      </ul>
      {/* --- Animation CSS (can be global or module-scoped) --- */}
      <style>{`
        .animate-scroll-logos {
          animation: scroll-logos var(--animation-duration, 20s) linear infinite;
          animation-direction: var(--animation-direction, forwards);
        }
        @keyframes scroll-logos {
          0%   { transform: translateX(0); }
          100% { transform: translateX(-50%); }
        }
      `}</style>
    </div>
  );
};
