'use client';

import { useRef } from 'react';
import { Home, MapPin, Users, Gem } from 'lucide-react';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { AnimatedText } from './ui/animated-text';
import { motion, useInView } from 'framer-motion';
import { useTranslations } from 'next-intl';

export function EssenceSection() {
  const t = useTranslations('essence-section');
  return (
    <section className='py-20 px-4 md:px-8 lg:px-16'>

      <div className="mx-auto max-w-6xl text-center w-fit">
        <h2 className="text-4xl md:text-5xl font-serif text-black dark:text-primary">
          {t('title')}
        </h2>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 208.33 7.34"
          className="w-80 mx-auto my-3 block align-middle leading-none"
          fill="currentColor"
        ><path d="M104.43,2.94c-1.86-1.1-3.91-1.79-6.01-2.13-2.11-.33-4.26-.25-6.38-.05-4.25.47-8.39,1.61-12.47,2.92l-6.13,2c-2.04.63-4.13,1.34-6.35,1.57-4.38.48-9.05-.73-12.37-3.92l.41.18-13.65-.04c-4.55-.03-9.1-.01-13.65-.07l-13.65-.16c-4.55-.1-9.63-.15-14.18-.28,4.55-.13,9.63-.19,14.18-.28l13.65-.16c4.55-.06,9.1-.05,13.65-.07l13.65-.04c.15,0,.29.06.39.16l.03.03c1.4,1.39,3.3,2.48,5.27,3.07,1.98.62,4.09.81,6.17.6,2.08-.2,4.1-.86,6.16-1.48,2.05-.64,4.11-1.28,6.17-1.88,4.13-1.21,8.35-2.29,12.67-2.64,2.16-.12,4.34-.11,6.48.3,2.12.42,4.17,1.22,5.96,2.42Z" /><path d="M117.55,2.84c1.79-1.18,3.85-1.96,5.97-2.33,2.12-.39,4.3-.43,6.45-.29,4.31.31,8.52,1.38,12.65,2.53l6.17,1.81c2.06.61,4.09,1.23,6.16,1.38,4.13.44,8.41-.54,11.88-3.09-1.39,1.68-3.36,2.81-5.44,3.53-2.1.66-4.36.89-6.55.65-2.2-.24-4.29-.95-6.33-1.55l-6.13-1.93c-4.08-1.25-8.22-2.37-12.46-2.8-4.2-.45-8.66-.11-12.38,2.1Z" /><path d="M104.43,2.94c1.79-1.2,3.84-2,5.96-2.42,2.13-.42,4.32-.42,6.48-.3,4.33.35,8.54,1.43,12.67,2.64l6.17,1.88c2.06.62,4.08,1.28,6.16,1.48,2.08.21,4.19.02,6.17-.59,1.97-.59,3.87-1.68,5.27-3.07l.02-.02c.11-.11.25-.16.39-.16l13.97.04,13.97.07,12.68.16c4.66.09,9.31.15,13.97.28-4.66.13-9.32.19-13.97.28l-12.68.16-13.97.07-13.97.04.41-.18c-3.31,3.19-7.98,4.4-12.37,3.92-2.21-.23-4.31-.94-6.35-1.57l-6.13-2c-4.08-1.31-8.22-2.45-12.47-2.92-2.12-.2-4.27-.27-6.38.06-2.1.35-4.15,1.03-6.01,2.14Z" /><path d="M43.87,2.84c1.64,1.38,3.6,2.33,5.64,2.82,2.04.54,4.17.65,6.24.39,2.08-.26,4.09-.9,6.14-1.53l6.14-1.91c4.13-1.17,8.34-2.25,12.68-2.56,2.16-.1,4.35-.09,6.49.33,2.12.46,4.18,1.24,5.95,2.46-1.9-1-3.95-1.64-6.04-1.91-2.1-.3-4.23-.21-6.33-.01-2.1.26-4.19.61-6.27,1.09-2.07.5-4.12,1.05-6.17,1.68l-6.16,1.81c-2.05.58-4.15,1.27-6.35,1.41-2.18.17-4.4-.05-6.48-.72-1.04-.33-2.03-.79-2.96-1.35-.93-.55-1.81-1.2-2.53-2Z" /><path d="M7,1.78c-1.43,1.05-4.47,1.16-4.47,1.16,0,0,3.05.11,4.47,1.16,1.43,1.05,2.32-.24,1.67-1.16.65-.92-.24-2.22-1.67-1.16Z" /><path d="M2.94,2.94c0,.39-.32.71-.71.71s-.71-.32-.71-.71.32-.71.71-.71.***********Z" /><path d="M201.32,4.11c1.43-1.05,4.48-1.16,4.48-1.16,0,0-3.05-.11-4.48-1.16-1.43-1.05-2.32.24-1.66,1.16-.66.92.23,2.22,1.66,1.16Z" /><path d="M205.39,2.94c0-.39.32-.71.71-.71s.***********-.32.71-.71.71-.71-.32-.71-.71Z" /></svg>
        <p className="text-md tracking-widest text-black dark:text-primary font-medium text-center mt-2 mb-12 leading-snug">
          {t('subtitle')}
        </p>
      </div>

      <ul className='grid gap-6 sm:grid-cols-2 xl:grid-cols-4'>
        <GridItem
          icon={<Gem className='h-4 w-4 text-black dark:text-primary' />}
          title={t('philosophy.title')}
          description={t('philosophy.description')}
          index={1}
        />
        <GridItem
          icon={<Home className='h-4 w-4 text-black dark:text-primary' />}
          title={t('experience.title')}
          description={t('experience.description')}
          index={2}
        />
        <GridItem
          icon={<MapPin className='h-4 w-4 text-black dark:text-primary' />}
          title={t('presence.title')}
          description={t('presence.description')}
          index={3}
        />
        <GridItem
          icon={<Users className='h-4 w-4 text-black dark:text-primary' />}
          title={t('service.title')}
          description={t('service.description')}
          index={4}
        />
      </ul>
    </section>
  );
}

interface GridItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  index: number;
}

const GridItem = ({ icon, title, description, index }: GridItemProps) => {
  const ref = useRef(null);
  const inView = useInView(ref, { once: false, margin: '-10% 0px' });

  return (
    <li className='list-none'>
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 30 }}
        animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.6, ease: 'easeOut', delay: index * 0.2 }}
        className='relative h-full min-h-[10rem] rounded-2xl dark:border dark:border-primary/20 p-2 md:rounded-3xl md:p-3'
      >
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className='relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border dark:shadow-[0px_0px_15px_0px_#FFFFE3] shadow-[0px_0px_15px_0px_#00000050] p-6 md:p-6 dark:border-0 dark:shadow-[0px_0px_27px_0px_#2D2D2D]'>
          <div className='relative flex flex-1 flex-col gap-3'>
            <div className='w-fit rounded-lg border border-gray-600 p-2'>
              {icon}
            </div>
            <div className='space-y-3'>
              <h3 className='-tracking-4 pt-0.5 font-sans text-xl font-semibold text-black md:text-2xl dark:text-primary'>
                {title}
              </h3>
              <AnimatedText
                className='font-sans text-sm text-black md:text-base dark:text-primary/60'
                text={description}
              />
            </div>
          </div>
        </div>
      </motion.div>
    </li>
  );
};