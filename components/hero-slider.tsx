'use client';
import { motion } from 'motion/react';
import React from 'react';
import { ImagesSlider } from '@/components/ui/image-slider';
import { SparklesCore } from './ui/sparkles';
import Image from 'next/image';
import { Button } from './ui/moving-border';

export function HeroSlider() {
  const images = [
    '/images/sliders/1.webp',
    '/images/sliders/2.webp',
    '/images/sliders/3.webp',
    '/images/sliders/4.webp',
    '/images/sliders/5.webp',
    '/images/sliders/6.webp',
    '/images/sliders/7.webp',
    '/images/sliders/8.webp',
    '/images/sliders/9.webp',
    '/images/sliders/10.webp',
    '/images/sliders/11.webp',
    '/images/sliders/12.webp',
    '/images/sliders/13.webp',
    '/images/sliders/14.webp',
  ];
  return (
    <ImagesSlider className='h-[100vh] w-full max-w-full' images={images} transitionStyle='blurFade'>
      <motion.div
        initial={{
          opacity: 0,
          y: -80,
        }}
        animate={{
          opacity: 1,
          y: 0,
        }}
        transition={{
          duration: 1,
        }}
        className='z-50 flex flex-col justify-center items-center'
      >
        <Image
          src='/logo.svg'
          loading='eager'
          fetchPriority='high'
          alt='logo'
          width={200}
          height={89.52}
        />

        <div className='w-[15rem] h-8 relative'>
          <SparklesCore
            background='transparent'
            minSize={0.4}
            maxSize={1}
            particleDensity={1200}
            className='w-full h-full'
            particleColor='#FFFFFF'
          />
        </div>
        <a
          href='https://www.mews.li/distributor/b33f587b-8ffc-417a-8af2-ab5700ddd7c3'
          target='_blank'
        >
          <Button>Book now →</Button>
        </a>
      </motion.div>
    </ImagesSlider>
  );
}
