// components/SocialIconButton.tsx

import { FC } from 'react';
import { Button } from '@/components/ui/button';
import {
  IconBrandFacebookFilled,
  IconBrandInstagramFilled,
  IconBrandXFilled,
  IconBrandYoutubeFilled,
} from '@tabler/icons-react';

interface SocialIconButtonProps {
  platform: 'facebook' | 'x' | 'instagram' | 'youtube';
  href: string;
}

const platformConfig = {
  facebook: {
    icon: <IconBrandFacebookFilled className='h-4 w-4' />,
    color: 'bg-[#1877F2] hover:bg-[#166FE0]',
    label: 'Facebook',
  },
  x: {
    icon: <IconBrandXFilled className='h-4 w-4' />,
    color: 'bg-black hover:bg-neutral-800',
    label: 'X',
  },
  instagram: {
    icon: <IconBrandInstagramFilled className='h-4 w-4' />,
    color: 'bg-gradient-to-tr from-yellow-400 via-pink-500 to-purple-600 hover:opacity-90',
    label: 'Instagram',
  },
  youtube: {
    icon: <IconBrandYoutubeFilled className='h-4 w-4' />,
    color: 'bg-[#FF0000] hover:bg-[#e60000]',
    label: 'YouTube',
  },
};

const SocialIconButton: FC<SocialIconButtonProps> = ({ platform, href }) => {
  const { icon, color, label } = platformConfig[platform];

  return (
    <a href={href} target='_blank' rel='noopener noreferrer' aria-label={label}>
      <Button
        className={`rounded-full ${color} text-white w-10 h-10 p-0`}
        style={{ cursor: 'pointer' }}
        variant='ghost'
        title={platform}
      >
        {icon}
      </Button>
    </a>
  );
};

export default SocialIconButton;
