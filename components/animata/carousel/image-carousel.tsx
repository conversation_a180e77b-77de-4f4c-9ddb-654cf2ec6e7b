import React, { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface ICarouselItem {
  id: number;
  title: string;
  image: string;
}

interface IImageCarouselProps {
  items: ICarouselItem[];
}

export default function ImageCarousel({ items: initialItems }: IImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(1);

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % initialItems.length);
  };

  const handlePrev = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + initialItems.length) % initialItems.length);
  };

  // Show 3 images: [previous, current, next]
  const visibleIndices = [
    (currentIndex - 1 + initialItems.length) % initialItems.length,
    currentIndex,
    (currentIndex + 1) % initialItems.length,
  ];

  const visibleItems = visibleIndices.map((index) => initialItems[index]);

  return (
    // ⬇️ Main wrapper — must be `relative` to anchor `absolute` buttons
    <div className="relative h-[500px] w-full max-w-[700px] sm:max-w-[700px] mx-auto overflow-hidden rounded-3xl border border-black-700 p-2">

      {/* ⬅️ LEFT Button */}
      <div
        onClick={handlePrev}
        className="z-50 absolute left-2 top-1/2 transform -translate-y-1/2 cursor-pointer p-2 rounded-full bg-white/30 hover:bg-white/50 backdrop-blur-sm"
      >
        <ChevronLeft className="w-6 h-6 text-black" />
      </div>

      {/* ➡️ RIGHT Button */}
      <div
        onClick={handleNext}
        className="z-50 absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer p-2 rounded-full bg-white/30 hover:bg-white/50 backdrop-blur-sm"
      >
        <ChevronRight className="w-6 h-6 text-black" />
      </div>

      {/* 🖼️ Images */}
      {visibleItems.map((item, index) => (
        <div
          key={item.id}
          className="absolute top-[20%] left-1/2 h-[250px] w-[200px] animate-fadeIn rounded-xl shadow-md"
          style={{
            backgroundImage: `url(${item.image})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            transform:
              index === 1
                ? "translateX(-50%) scale(1.1)"
                : index === 0
                  ? "translateX(-150%) rotate(-10deg)"
                  : "translateX(50%) rotate(10deg)",
            transition: "transform 0.5s ease, filter 0.5s ease",
            filter: index === 1 ? "none" : "blur(4px)",
            zIndex: index === 1 ? 3 : 1,
          }}
        />
      ))}
    </div>
  );
}
