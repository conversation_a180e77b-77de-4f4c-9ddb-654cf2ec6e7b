'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from 'lucide-react';
import { JSX, useEffect, useState, useRef } from 'react';
import { motion } from 'motion/react';
import Image from 'next/image';
import { useInView } from 'framer-motion';

import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel-room';

export interface Gallery4Item {
  id: string;
  title: string;
  description: string | JSX.Element;
  href: string;
  image: string;
}

export interface Gallery4Props {
  title?: string;
  description?: string | JSX.Element;
  items: Gallery4Item[];
}

function Gallery4Card({
  item,
  index,
}: {
  item: Gallery4Item;
  index: number;
}) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, margin: '0px 10%' });

  return (
    <CarouselItem
      key={item.id}
      className="w-full sm:max-w-[500px] shrink-0 grow-0 px- md:px-3"
    >
      <motion.a
        ref={ref}
        initial={{ opacity: 0, y: 20 }}
        animate={
          isInView
            ? {
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.6,
                delay: index * 0.05,
                ease: 'easeOut',
              },
            }
            : {}
        }
        href={item.href}
        className="group rounded-xl block h-full"
      >
        <div className="relative h-full min-h-[27rem] max-w-full overflow-hidden rounded-xl md:aspect-[5/4] lg:aspect-[16/9]">
          <Image
            src={item.image}
            alt={item.title}
            fill
            className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
          <div className="absolute inset-0 h-full bg-[linear-gradient(hsl(var(--primary)/0),hsl(var(--primary)/0.4),hsl(var(--primary)/0.8)_100%)] mix-blend-multiply" />
          <div className="absolute inset-0 flex flex-col items-center justify-center text-center p-4 bg-black/40 text-white text-primary-foreground md:p-8">
            <div className="text-2xl md:text-3xl font-semibold mb-4 md:mb-3 md:pt-4 lg:pt-4 text-white">
              {item.title}
            </div>
            <div className="mb-8 line-clamp-2 md:mb-12 lg:mb-9 text-white">
              {item.description}
            </div>
            <div className="flex items-center text-sm text-white">
              Read more <ArrowRight className="h-5 ml-1" />
            </div>
          </div>
        </div>
      </motion.a>
    </CarouselItem>
  );
}

const Gallery4 = ({ items }: Gallery4Props) => {
  const [carouselApi, setCarouselApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    if (!carouselApi) return;

    const updateSelection = () => {
      setCanScrollPrev(carouselApi.canScrollPrev());
      setCanScrollNext(carouselApi.canScrollNext());
      setCurrentSlide(carouselApi.selectedScrollSnap());
    };

    updateSelection();
    carouselApi.on('select', updateSelection);

    return () => {
      carouselApi.off('select', updateSelection);
    };
  }, [carouselApi]);

  return (
    <section className="pt-0 pb-10">
      <div className="container mx-auto">
        {/* Arrows */}
        <div className="mb-4 flex items-end justify-center gap-2">
          <Button
            size="icon"
            variant="ghost"
            onClick={() => carouselApi?.scrollPrev()}
            disabled={!canScrollPrev}
            className="cursor-pointer disabled:cursor-not-allowed text-black dark:text-white hover:bg-black/10 dark:hover:bg-white/10"
            title='Previous'
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <Button
            size="icon"
            variant="ghost"
            onClick={() => carouselApi?.scrollNext()}
            disabled={!canScrollNext}
            className="cursor-pointer disabled:cursor-not-allowed text-black dark:text-white hover:bg-black/10 dark:hover:bg-white/10"
            title='Next'
          >
            <ArrowRight className="w-5 h-5" />
          </Button>
        </div>

        {/* Carousel */}
        <Carousel
          setApi={setCarouselApi}
          opts={{
            dragFree: true, // key line to allow smooth free dragging
            loop: false,    // set true if you want infinite wrap-around
          }}
        >
          <CarouselContent className="ml-0">
            {items.map((item, index) => (
              <Gallery4Card key={item.id} item={item} index={index} />
            ))}
          </CarouselContent>
        </Carousel>

        {/* Dots */}
        <div className="mt-8 flex justify-center gap-2">
          {items.map((_, index) => (
            <button
              key={index}
              className={`h-11 w-11 flex items-center justify-center rounded-full focus:outline-none`}
              onClick={() => carouselApi?.scrollTo(index)}
              aria-label={`Go to slide ${index + 1}`}
            >
              <span
                className={`h-2 w-2 rounded-full transition-colors ${currentSlide === index ? 'bg-primary' : 'bg-primary/20'
                  }`}
              />
            </button>
          ))}
        </div>
      </div>
    </section>
  );
};

export { Gallery4 };
