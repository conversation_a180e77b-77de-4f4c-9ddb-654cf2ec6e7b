{"name": "noir-d-ivoire", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.6", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@tabler/icons-react": "^3.34.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.9", "lucide-react": "^0.525.0", "motion": "^12.23.0", "next": "15.3.5", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-modal": "^3.16.3", "tailwind-merge": "^3.3.1", "yet-another-react-lightbox": "^3.25.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal": "^3.16.3", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "typescript-eslint": "^8.35.1", "webpack-bundle-analyzer": "^4.10.2"}}